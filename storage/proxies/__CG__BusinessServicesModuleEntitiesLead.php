<?php

namespace CMS\Proxy\__CG__\BusinessServicesModule\Entities;


/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Lead extends \BusinessServicesModule\Entities\Lead implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'id', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'company', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'valid', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'offerId', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'lastError', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'lastErrorOn', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'processed', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'deleted', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'dtc', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'dtm', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'partnerServicesInformation', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'partnerCashplusService', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'bdgLead'];
        }

        return ['__isInitialized__', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'id', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'company', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'valid', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'offerId', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'lastError', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'lastErrorOn', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'processed', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'deleted', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'dtc', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'dtm', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'partnerServicesInformation', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'partnerCashplusService', '' . "\0" . 'BusinessServicesModule\\Entities\\Lead' . "\0" . 'bdgLead'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Lead $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load(): void
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized(): bool
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized): void
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null): void
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer(): ?\Closure
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null): void
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner(): ?\Closure
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties(): array
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getCompany(): \Entities\Company
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompany', []);

        return parent::getCompany();
    }

    /**
     * {@inheritDoc}
     */
    public function isValid(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isValid', []);

        return parent::isValid();
    }

    /**
     * {@inheritDoc}
     */
    public function setValid(bool $valid)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setValid', [$valid]);

        return parent::setValid($valid);
    }

    /**
     * {@inheritDoc}
     */
    public function markAsValid(): \BusinessServicesModule\Entities\Lead
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'markAsValid', []);

        return parent::markAsValid();
    }

    /**
     * {@inheritDoc}
     */
    public function getOfferId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOfferId', []);

        return parent::getOfferId();
    }

    /**
     * {@inheritDoc}
     */
    public function getLastError(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastError', []);

        return parent::getLastError();
    }

    /**
     * {@inheritDoc}
     */
    public function setLastError(?string $lastError): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLastError', [$lastError]);

        parent::setLastError($lastError);
    }

    /**
     * {@inheritDoc}
     */
    public function getLastErrorOn(): ?\DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastErrorOn', []);

        return parent::getLastErrorOn();
    }

    /**
     * {@inheritDoc}
     */
    public function setLastErrorOn(?\DateTime $lastErrorOn): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLastErrorOn', [$lastErrorOn]);

        parent::setLastErrorOn($lastErrorOn);
    }

    /**
     * {@inheritDoc}
     */
    public function isProcessed(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isProcessed', []);

        return parent::isProcessed();
    }

    /**
     * {@inheritDoc}
     */
    public function isDeleted(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isDeleted', []);

        return parent::isDeleted();
    }

    /**
     * {@inheritDoc}
     */
    public function setDeleted(bool $deleted): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDeleted', [$deleted]);

        parent::setDeleted($deleted);
    }

    /**
     * {@inheritDoc}
     */
    public function getDtc(): \DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtc', []);

        return parent::getDtc();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtm(): \DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtm', []);

        return parent::getDtm();
    }

    /**
     * {@inheritDoc}
     */
    public function markAsProcessed(): \BusinessServicesModule\Entities\Lead
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'markAsProcessed', []);

        return parent::markAsProcessed();
    }

    /**
     * {@inheritDoc}
     */
    public function markAsDeleted(): \BusinessServicesModule\Entities\Lead
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'markAsDeleted', []);

        return parent::markAsDeleted();
    }

    /**
     * {@inheritDoc}
     */
    public function getPartnerServicesInformation(): ?\BusinessServicesModule\Entities\PartnerServicesInformation
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPartnerServicesInformation', []);

        return parent::getPartnerServicesInformation();
    }

    /**
     * {@inheritDoc}
     */
    public function setPartnerServicesInformation(?\BusinessServicesModule\Entities\PartnerServicesInformation $partnerServicesInformation): \BusinessServicesModule\Entities\Lead
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPartnerServicesInformation', [$partnerServicesInformation]);

        return parent::setPartnerServicesInformation($partnerServicesInformation);
    }

    /**
     * {@inheritDoc}
     */
    public function hasPartnerServicesInformation(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasPartnerServicesInformation', []);

        return parent::hasPartnerServicesInformation();
    }

    /**
     * {@inheritDoc}
     */
    public function getPartnerCashplusService(): ?\CashplusModule\Entities\PartnerCashplusService
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPartnerCashplusService', []);

        return parent::getPartnerCashplusService();
    }

    /**
     * {@inheritDoc}
     */
    public function hasPartnerCashplusService(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasPartnerCashplusService', []);

        return parent::hasPartnerCashplusService();
    }

    /**
     * {@inheritDoc}
     */
    public function getBdgLead(): ?\BusinessDataModule\Entities\Lead
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBdgLead', []);

        return parent::getBdgLead();
    }

    /**
     * {@inheritDoc}
     */
    public function setBdgLead(?\BusinessDataModule\Entities\Lead $lead): \BusinessServicesModule\Entities\Lead
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setBdgLead', [$lead]);

        return parent::setBdgLead($lead);
    }

    /**
     * {@inheritDoc}
     */
    public function hasBdgLead(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasBdgLead', []);

        return parent::hasBdgLead();
    }

    /**
     * {@inheritDoc}
     */
    public function getCustomer(): \Entities\Customer
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCustomer', []);

        return parent::getCustomer();
    }

    /**
     * {@inheritDoc}
     */
    public function isBarclays(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isBarclays', []);

        return parent::isBarclays();
    }

    /**
     * {@inheritDoc}
     */
    public function jsonSerialize(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'jsonSerialize', []);

        return parent::jsonSerialize();
    }

}
