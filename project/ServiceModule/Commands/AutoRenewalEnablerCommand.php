<?php

namespace ServiceModule\Commands;

use CompaniesHouseModule\Repositories\MemberRepository;
use CompaniesHouseModule\Services\SubmissionHandler;
use Entities\Company;
use Entities\Service;
use OmnipayModule\Providers\OmnipayCardProvider;
use Psr\Log\LoggerInterface;
use Services\CompanyService;
use ServiceSettingsModule\Facades\ServiceSettingsFacade;
use ServiceSettingsModule\Services\ServiceSettingsService;

/**
 * @description Enable auto-renewal for companies with services that are using our address.
 * Must NOT be added to the crontab.
 * Wil be run manually by <PERSON>'s request.
 */
class AutoRenewalEnablerCommand
{
    public const DAYS_BEFORE_SENDING_FIRST_RENEWAL_REMINDER = 21;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var CompanyService
     */
    private $companyService;

    /**
     * @var ServiceSettingsService
     */
    private $serviceSettingsService;

    /**
     * @var MemberRepository
     */
    private $chMemberRepository;

    /**
     * @var SubmissionHandler
     */
    private $submissionHandler;

    /**
     * @var OmnipayCardProvider
     */
    private $omnipayCardProvider;

    /**
     * @var ServiceSettingsFacade
     */
    private $serviceSettingsFacade;

    public function __construct(
        LoggerInterface $logger,
        companyService $companyService,
        ServiceSettingsService $serviceSettingsService,
        MemberRepository $chMemberRepository,
        SubmissionHandler $submissionHandler,
        OmnipayCardProvider $omnipayCardProvider,
        ServiceSettingsFacade $serviceSettingsFacade,
    ) {
        $this->logger = $logger;
        $this->companyService = $companyService;
        $this->serviceSettingsService = $serviceSettingsService;
        $this->chMemberRepository = $chMemberRepository;
        $this->submissionHandler = $submissionHandler;
        $this->omnipayCardProvider = $omnipayCardProvider;
        $this->serviceSettingsFacade = $serviceSettingsFacade;
    }

    public function execute(bool $dryRun = true): void
    {
        if ($dryRun) {
            $this->logger->debug('Executing AutoRenewal Command in DRY RUN MODE.');
        }

        $enabledCompanies = $skippedCompanies = $processedCompanies = 0;

        $companies = $this->companyService->getEligibleCompaniesToEnableAutoRenewal();

        if (empty($companies)) {
            $this->logger->info('No company services found with auto renewal disabled.');

            return;
        }

        $totalCompanies = count($companies);

        $this->logger->info(sprintf(
            '%s companies found with auto renewal disabled.',
            $totalCompanies
        ));

        /** @var Company $company */
        foreach ($companies as $company) {
            $this->logger->info("\n\n");
            $companyId = $company->getId();
            $companyName = $company->getCompanyName();

            $this->logger->info(
                sprintf(
                    '%sProcessing Company %s - %s.',
                    $dryRun ? '(DRY-RUN) ' : '',
                    $companyId,
                    $companyName
                )
            );

            if (!$company->isIncorporated()) {
                $this->logger->info(
                    sprintf(
                        '%s[SKIPPED] Company %s - %s is not incorporated.',
                        $dryRun ? '(DRY-RUN) ' : '',
                        $companyId,
                        $companyName
                    )
                );

                ++$skippedCompanies;
                ++$processedCompanies;
                continue;
            }

            //            if (!$this->submissionHandler->isCompanyIdValid($company)) {
            //                $this->logger->info(
            //                    sprintf(
            //                        '%s[SKIPPED] Company %s - %s ID Check is not Valid.',
            //                        $dryRun ? '(DRY-RUN) ' : '',
            //                        $companyId,
            //                        $companyName
            //                    )
            //                );
            //
            //                $skippedCompanies++;
            //                $processedCompanies++;
            //                continue;
            //            }

            if ($company->isUsingMsgRegisteredOffice()) {
                $this->logger->info(sprintf(
                    '%sCompany %s - %s is using our address.',
                    $dryRun ? '(DRY-RUN) ' : '',
                    $companyId,
                    $companyName
                ));

                $this->enableCompanyAutoRenewal($company, $dryRun) ? $enabledCompanies++ : $skippedCompanies++;
                ++$processedCompanies;
                continue;
            }

            if ($this->chMemberRepository->hasIdCompanyMembersUsingMsgServiceAddress($company)) {
                $this->logger->info(
                    sprintf(
                        '%sCompany %s - %s has CH members using our address.',
                        $dryRun ? '(DRY-RUN) ' : '',
                        $company->getId(),
                        $company->getCompanyName()
                    )
                );

                $this->enableCompanyAutoRenewal($company, $dryRun) ? $enabledCompanies++ : $skippedCompanies++;
                ++$processedCompanies;
                continue;
            }

            $this->logger->info(
                sprintf(
                    '%s[SKIPPED] Company %s - %s and its members are NOT using our address.',
                    $dryRun ? '(DRY-RUN) ' : '',
                    $company->getId(),
                    $company->getCompanyName()
                )
            );
            ++$skippedCompanies;
            ++$processedCompanies;
        }

        $this->logger->info(sprintf(
            '
            %sFinished processing.
            Total companies: %s | Processed: %s | Enabled: %s | Skipped: %s
            ',
            $dryRun ? '(DRY-RUN) ' : '',
            $totalCompanies,
            $processedCompanies,
            $enabledCompanies,
            $skippedCompanies
        ));
    }

    private function enableCompanyAutoRenewal(Company $company, bool $dryRun): bool
    {
        $companyId = $company->getId();
        $companyName = $company->getCompanyName();

        $services = [];
        foreach ($company->getEnabledParentServicesWithType(Service::TYPE_REGISTERED_OFFICE) as $service) {
            $services[] = $service;
        }
        foreach ($company->getEnabledParentServicesWithType(Service::TYPE_SERVICE_ADDRESS) as $service) {
            $services[] = $service;
        }

        $packageWithAddressType = $productWithAddressType = null;
        $serviceReminderHasBeenSent = null;

        /** @var Service $service */
        foreach ($services as $service) {
            if (!$service->getDtExpires() || !$service->getDtStart()) {
                continue;
            }

            if ($this->hasFirstRenewalReminderBeenSent($service)) {
                $serviceReminderHasBeenSent = $service;
                continue;
            }

            if ($this->isPackageWithAddressType($service)) {
                $packageWithAddressType = $service;
                break;
            }

            if ($this->isProductOfAddressType($service)) {
                $productWithAddressType = $service;
            }
        }

        $serviceToEnableAutoRenewal = $packageWithAddressType ?? $productWithAddressType;

        if ($serviceToEnableAutoRenewal) {
            $this->logger->info(
                sprintf(
                    '%s%s FOUND (%s) for Company %s - %s.',
                    $dryRun ? '(DRY-RUN) ' : '',
                    $packageWithAddressType ? 'Package with Address Service' : 'Address Service Product',
                    $service->getServiceName(),
                    $companyId,
                    $companyName
                )
            );

            return $this->enableServiceAutoRenewal($service, $dryRun);
        }

        if ($serviceReminderHasBeenSent) {
            $this->logger->info(
                sprintf(
                    '%s[SKIPPED] Company %s - %s has address type service %s - %s BUT renewal email reminder has already been sent.',
                    $dryRun ? '(DRY-RUN) ' : '',
                    $companyId,
                    $companyName,
                    $serviceReminderHasBeenSent->getId(),
                    $serviceReminderHasBeenSent->getServiceName()
                )
            );

            return false;
        }

        $this->logger->info(
            sprintf(
                '%s[SKIPPED] Company %s - %s has NO address type service.',
                $dryRun ? '(DRY-RUN) ' : '',
                $companyId,
                $companyName
            )
        );

        return false;
    }

    private function enableServiceAutoRenewal(Service $service, bool $dryRun): bool
    {
        $company = $service->getCompany();

        $customer = $company->getCustomer();
        $customerId = $customer->getId();

        if (!$this->omnipayCardProvider->hasPaymentMethods($customerId)) {
            $this->logger->info(
                sprintf(
                    '%s[SKIPPED] Company %s - %s, Customer %s - %s has NO payment methods.',
                    $dryRun ? '(DRY-RUN) ' : '',
                    $company->getId(),
                    $company->getCompanyName(),
                    $customerId,
                    $customer->getEmail()
                )
            );

            return false;
        }

        try {
            $serviceSetting = $this->serviceSettingsService->getSettingsByService($service);

            if (!$dryRun) {
                $this->serviceSettingsFacade->enableAutoRenewal($serviceSetting);
            }

            $this->logger->info(sprintf(
                '%s[SUCCESS] Company: %s - %s. Auto renewal enabled for service %s - %s, service setting %s - %s.',
                $dryRun ? '(DRY-RUN) ' : '',
                $company->getId(),
                $company->getCompanyName(),
                $service->getId(),
                $service->getServiceName(),
                $serviceSetting->getId(),
                $serviceSetting->getServiceTypeId()
            ));

            return true;
        } catch (\Exception $e) {
            $company = $service->getCompany();

            $this->logger->warning(sprintf(
                '%s[SKIPPED] Failed to enable auto renewal for Company: %s - %s. service %s - %s. Error: %s',
                $dryRun ? '(DRY-RUN) ' : '',
                $company->getId(),
                $company->getCompanyName(),
                $service->getId(),
                $service->getServiceName(),
                $e->getMessage()
            ));

            return false;
        }
    }

    private function isPackageWithAddressType(Service $service): bool
    {
        if (!$service->isCorePackageType()) {
            return false;
        }

        return
            $service->containsType(Service::TYPE_REGISTERED_OFFICE)
            || $service->containsType(Service::TYPE_SERVICE_ADDRESS)
        ;
    }

    private function isProductOfAddressType(Service $service): bool
    {
        return in_array($service->getServiceType(), [
            Service::$types[Service::TYPE_REGISTERED_OFFICE],
            Service::$types[Service::TYPE_SERVICE_ADDRESS],
        ]);
    }

    private function hasFirstRenewalReminderBeenSent(Service $service): bool
    {
        $daysBeforeFirstRenewalReminderString = sprintf(
            '-%s days',
            self::DAYS_BEFORE_SENDING_FIRST_RENEWAL_REMINDER
        );

        $firstRenewalReminder = $service->getDtExpires()
            ->modify($daysBeforeFirstRenewalReminderString)
            ->setTime(0, 0, 0);

        return new \DateTime() >= $firstRenewalReminder;
    }
}
