<?php

namespace MailScanModule\Views;

class SearchResultRowView implements \JsonSerializable
{
    public const TYPE_COMPANY = 'COMPANY';
    public const TYPE_OFFICER = 'OFFICER';

    public const ACTION_TYPE_PRINT_LABEL = 'Print Label';
    public const ACTION_TYPE_LOG_RTS = 'Log RTS';

    /**
     * @var string
     */
    private $id;

    /**
     * @var string
     */
    private $name;

    /**
     * @var string
     */
    private $companyName;

    /**
     * @var string
     */
    private $linkToRelatedEntity;

    /**
     * @var string
     */
    private $site;

    /**
     * @var ServiceView[]
     */
    private $services;

    /**
     * @var Action
     */
    private $action;

    /**
     * @var string|null
     */
    private $mailForwardingId;

    /**
     * @var string
     */
    private $address1;

    /**
     * @var string
     */
    private $address2;

    /**
     * @var string
     */
    private $address3;

    /**
     * @var string
     */
    private $city;

    /**
     * @var string
     */
    private $countryId;

    /**
     * @var string
     */
    private $postcode;

    /**
     * @var bool
     */
    private $isProduction;

    /**
     * @var string
     */
    private string $siteName;

    public function __construct(
        string $id,
        string $name,
        string $companyName,
        string $linkToRelatedEntity,
        string $site,
        string $siteName,
        array $services,
        Action $action,
        bool $isProduction,
        ?string $mailForwardingId,
        ?string $address1 = null,
        ?string $address2 = null,
        ?string $address3 = null,
        ?string $city = null,
        ?string $countryId = null,
        ?string $postcode = null,
    ) {
        $this->id = $id;
        $this->name = $name;
        $this->companyName = $companyName;
        $this->linkToRelatedEntity = $linkToRelatedEntity;
        $this->site = $site;
        $this->siteName = $siteName;
        $this->services = $services;
        $this->action = $action;
        $this->isProduction = $isProduction;
        $this->mailForwardingId = $mailForwardingId;
        $this->address1 = $address1;
        $this->address2 = $address2;
        $this->address3 = $address3;
        $this->city = $city;
        $this->countryId = $countryId;
        $this->postcode = $postcode;
    }

    public static function from(
        string $id,
        string $name,
        string $companyName,
        string $linkToRelatedEntity,
        string $site,
        string $siteName,
        array $services,
        Action $action,
        bool $isProduction,
        ?string $mailForwardingId,
        ?string $address1 = null,
        ?string $address2 = null,
        ?string $address3 = null,
        ?string $city = null,
        ?string $countryId = null,
        ?string $postcode = null,
    ): self {
        return new self(
            $id,
            $name,
            $companyName,
            $linkToRelatedEntity,
            $site,
            $siteName,
            $services,
            $action,
            $isProduction,
            $mailForwardingId,
            $address1,
            $address2,
            $address3,
            $city,
            $countryId,
            $postcode
        );
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->getFormattedName() ?? $this->getCompanyName();
    }

    public function getLinkToRelatedEntity()
    {
        return $this->linkToRelatedEntity;
    }

    public function getSite(): string
    {
        return $this->site;
    }

    public function getSiteNameAndId(): string
    {
        return $this->siteName;
    }

    /**
     * @return ServiceView[]
     */
    public function getServices(): array
    {
        return $this->services;
    }

    public function getLastServiceType(): ?string
    {
        $service = $this->getLastService();

        return $service ? $service->getType() : null;
    }

    public function isAllSpecial(): bool
    {
        $service = $this->getLastService();

        return $service && $service->isAllSpecial();
    }

    public function getLastService(): ?ServiceView
    {
        return reset($this->services) ?? null;
    }

    public function getAction(): Action
    {
        return $this->action;
    }

    public function isProduction(): bool
    {
        return $this->isProduction;
    }

    public function hasServices(): bool
    {
        return !empty($this->services);
    }

    public function getMailForwardingId(): ?string
    {
        return $this->mailForwardingId;
    }

    public function getAddress1(): ?string
    {
        return $this->address1;
    }

    public function getAddress2(): ?string
    {
        return $this->address2;
    }

    public function getAddress3(): ?string
    {
        return $this->address3;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function getCountryId(): ?string
    {
        return $this->countryId;
    }

    public function getPostcode(): ?string
    {
        return $this->postcode;
    }

    public function jsonSerialize(): string
    {
        try {
            return urlencode(
                json_encode(
                    [
                        'id' => $this->id,
                        'name' => $this->name,
                        'companyName' => $this->companyName,
                        'site' => $this->site,
                        'serviceType' => $this->getMailForwardingId() ?? $this->getLastServiceType(),
                        'serviceName' => $this->getMailForwardingId() ? 'MAIL FORWARDING' : $this->getLastServiceType(),
                        'isAllSpecial' => $this->isAllSpecial(),
                        'address1' => $this->getAddress1(),
                        'address2' => $this->getAddress2(),
                        'address3' => $this->getAddress3(),
                        'city' => $this->getCity(),
                        'countryId' => $this->getCountryId(),
                        'postcode' => $this->getPostcode(),
                        'isProduction' => $this->isProduction(),
                    ]
                )
            );
        } catch (\JsonException $e) {
            throw $e;
        }
    }

    public function getCompanyName(): string
    {
        return str_replace('|', '<br>', $this->companyName);
    }

    public function getFormattedName(): ?string
    {
        if (!empty($this->name)) {
            return str_replace('|', '<br>', $this->name);
        }

        return null;
    }
}
