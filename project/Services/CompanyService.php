<?php

namespace Services;

use Libs\CHFiling\Core\Company;
use Libs\CHFiling\core\PDF\CompanySummary;
use Libs\CHFiling\Core\Request\Form\Incorporation\CompanyIncorporation;
use CompanyModule\Contracts\IDeleteCompany;
use CompanyModule\Entities\Settings\CompanySetting;
use CompanyModule\Repositories\CompanySettingsRepository;
use CustomerModule\Entities\BusinessPhoneOption;
use Models\OldModels\DeleteCompany;
use Dispatcher\Events\CompanyDeletedEvent;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NoResultException;
use Entities\Company as CompanyEntity;
use Entities\CompanyHouse\FormSubmission;
use Entities\CompanyHouse\FormSubmission\CompanyIncorporation as CompanyIncorporationEntity;
use Entities\Customer as CustomerEntity;
use Entities\Customer;
use Entities\EntityAbstract;
use Entities\Order;
use Entities\Payment\Token;
use Config\Constants\EventLocator;
use Exception;
use Exceptions\Business\CompanyException;
use Factories\Front\CompanyViewFactory;
use Libs\CHFiling\Core\UtilityClass\Identification;
use Libs\CHFiling\Core\Request\Form\Incorporation\IncorporationCorporateDirector;
use Libs\CHFiling\Core\Request\Form\Incorporation\IncorporationPersonDirector;
use Models\Products\NomineeDirector;
use Models\Products\Product;
use Repositories\CompanyRepository;
use Repositories\OrderItemRepository;
use Symfony\Component\EventDispatcher\EventDispatcher;
use UserModule\Contracts\ICustomer;
use Utils\NetteSmartObject;

class CompanyService extends NetteSmartObject implements IDeleteCompany
{
    /**
     * @var CompanyRepository
     */
    private $repository;

    /**
     * @var OrderItemRepository
     */
    private $orderItemRepository;

    /**
     * @var CompanyViewFactory
     */
    private $companyViewFactory;

    /**
     * @var EventDispatcher
     */
    private $eventDispatcher;

    /**
     * @var CompanySettingsRepository
     */
    private $companySettingsRepository;

    public function __construct(
        CompanyRepository $repository,
        OrderItemRepository $orderItemRepository,
        CompanyViewFactory $companyViewFactory,
        EventDispatcher $eventDispatcher,
        CompanySettingsRepository $companySettings
    )
    {
        $this->repository = $repository;
        $this->orderItemRepository = $orderItemRepository;
        $this->companyViewFactory = $companyViewFactory;
        $this->eventDispatcher = $eventDispatcher;
        $this->companySettingsRepository = $companySettings;
    }

    /**
     * @param EntityAbstract|EntityAbstract[]|NULL $entity
     */
    public function flush($entity = NULL)
    {
        $this->repository->flush($entity);
    }

    /**
     * @param CompanyEntity $company
     * @return CompanyEntity
     */
    public function saveCompany(CompanyEntity $company)
    {
        return $this->repository->saveEntity($company);
    }

    /**
     * @param int $id
     * @return CompanyEntity|null
     */
    public function getCompanyById($id)
    {
        return $this->repository->find($id);
    }

    /**
     * @param string $companyNumber
     * @return CompanyEntity|null
     */
    public function getCompanyByCompanyNumber($companyNumber)
    {
        return $this->repository->findOneBy(['companyNumber' => $companyNumber]);
    }

    /**
     * @param string $name
     * @return CompanyEntity|null
     */
    public function getCompanyByCompanyName($name)
    {
        return $this->repository->findOneBy(['companyName' => $name]);
    }


    /**
     * @param string $name
     * @return CompanyEntity[]
     */
    public function getCompaniesByCompanyName(string $name): array
    {
        $q = $this->repository->createQueryBuilder('c')
            ->where('c.companyName LIKE :companyName')
            ->setMaxResults(20)
            ->setParameter('companyName', sprintf('%%%s%%', $name))
            ->getQuery();

        return $q->getResult();
    }

    /**
     * @param CustomerEntity $customer
     * @return CompanyEntity[]
     */
    public function getCustomerCompanies(CustomerEntity $customer)
    {
        return $this->repository->findBy(['customer' => $customer]);
    }

    /**
     * @param CustomerEntity $customer
     * @return array CompanyView[]
     */
    public function getCustomerCompaniesViews(CustomerEntity $customer)
    {
        $companies = $this->getCustomerCompanies($customer);
        $companyViews = [];
        foreach ($companies as $company) {
            $companyViews[] = $this->companyViewFactory->create($company);
        }
        return $companyViews;
    }

    /**
     * @param CustomerEntity $customer
     * @return array CompanyView[]
     */
    public function getCustomerServiceCompaniesViews(CustomerEntity $customer)
    {
        $companies = $this->repository->getCustomerServiceCompanies($customer);
        $companyViews = [];
        foreach ($companies as $company) {
            $companyViews[] = $this->companyViewFactory->create($company);
        }
        return $companyViews;
    }

    /**
     * @param CustomerEntity $customer
     * @return CompanyEntity[]
     */
    public function getUnsubmittedCompanies(CustomerEntity $customer)
    {
        return $this->repository->getUnsubmittedCompanies($customer);
    }

    /**
     * @return CompanyEntity[]
     */
    public function getCompaniesWithActiveServices()
    {
        return $this->repository->getCompaniesWithActiveServices();
    }

    /**
     * @param CustomerEntity|ICustomer $customer
     * @param int $id
     * @throws CompanyException
     * @return CompanyEntity
     */
    public function getCustomerCompany(CustomerEntity|ICustomer $customer, $id)
    {
        $company = $this->repository->optionalOneBy(['customer' => $customer, 'companyId' => $id]);

        if (!$company) {
            throw CompanyException::doesNotExist($id);
        }
        return $company;
    }

    /**
     * @param CustomerEntity $customer
     * @param string $companyNumber
     * @throws CompanyException
     * @return CompanyEntity
     */
    public function getCustomerCompanyByCompanyNumber(CustomerEntity $customer, $companyNumber)
    {
        $company = $this->repository->optionalOneBy(['customer' => $customer, 'companyNumber' => $companyNumber]);
        if (!$company) {
            throw CompanyException::doesNotExist($companyNumber);
        }
        return $company;
    }

    /**
     * @param CompanyEntity $company
     * @param CustomerEntity $customer
     * @return bool
     */
    public function doesCompanyBelongToCustomer(CompanyEntity $company, CustomerEntity $customer)
    {
        return $company->getCustomer()->isEqual($customer);
    }

    /**
     * @param Order $order
     * @return CompanyEntity|NULL
     */
    public function getCompanyByOrder(Order $order)
    {
        return $this->repository->findOneBy(['order' => $order]);
    }

    public function changeToDeleted(CompanyEntity $company, string $message): DeleteCompany
    {
        $deletedCompany = DeleteCompany::fromCompany($company, $message);
        $deletedCompany->save();

        $company->markAsDeleted();

        $this->repository->saveEntity($company);

        $this->eventDispatcher->dispatch(
            new CompanyDeletedEvent(
                $company
            ),
            EventLocator::COMPANY_DELETED
        );
        return $deletedCompany;
    }

    /**
     * @param CustomerEntity $customer
     * @param bool $excludeDeleted
     * @return array
     */
    public function getCustomerIncorporatedCompanies(Customer $customer, $excludeDeleted = FALSE)
    {
        return $this->repository->getCustomerIncorporatedCompanies($customer, $excludeDeleted);
    }

    /**
     * Provide saving nominee director to customer company
     *
     * @param Company $company
     * @param NomineeDirector $director
     * @throws Exception
     */
    public function addNomineeDirectorToCompany(Company $company, NomineeDirector $director)
    {
        /** @var CompanyIncorporation $companyIncorporation */
        $companyIncorporation = $company->getLastIncorporationFormSubmission()->getForm();

        if ($director->isOk2show() == FALSE) {
            throw new Exception("{$director->getLngTitle()} can not be added!");
        } elseif ($companyIncorporation->hasNomineeDirector() == FALSE) {

            /************************ person ******************************/
            /** @var IncorporationPersonDirector $directorPerson */
            $directorPerson = $companyIncorporation->getNewIncPerson('dir');
            $directorPerson->setNominee(TRUE);

            $address = $directorPerson->getAddress();
            $address->setPremise($director->personAddress1);
            $address->setStreet($director->personAddress2);
            $address->setThoroughfare($director->personAddress3);
            $address->setPostTown($director->personTown);
            $address->setCounty($director->personCounty);
            $address->setPostcode($director->personPostcode);
            $address->setCountry($director->personCountryId); /** @phpstan-ignore-line */
            $directorPerson->setAddress($address);

            $person = $directorPerson->getPerson();
            $person->setForename($director->personFirstname);
            $person->setSurname($director->personSurname);
            $person->setNationality($director->personNationality);
            $person->setOccupation($director->personOccupation);
            $person->setCountryOfResidence($director->personCountryOfResidence);
            $person->setDob($director->personDob);

            $directorPerson->setResidentialAddress($address);
            $directorPerson->setPerson($person);
            $directorPerson->setConsentToAct(TRUE);
            $directorPerson->setNomineeType($director->getNomineeType());

            $directorPerson->save();


            /************************ corporate ******************************/
            if ($director->isCorporate()) {
                /** @var IncorporationCorporateDirector $directorCorporate */
                $directorCorporate = $companyIncorporation->getNewIncCorporate(
                    'dir'
                );
                $directorCorporate->setNominee(TRUE);

                $corporate = $directorCorporate->getCorporate();
                $corporate->setCorporateName($director->corporateCompanyName);
                $corporate->setForename($director->corporateFirstName);
                $corporate->setSurname($director->corporateLastName);
                $directorCorporate->setCorporate($corporate);

                $address = $directorCorporate->getAddress();
                $address->setPremise($director->corporateAddress1);
                $address->setStreet($director->corporateAddress2);
                $address->setThoroughfare($director->corporateAddress3);
                $address->setPostTown($director->corporateTown);
                $address->setCounty($director->corporateCounty);
                $address->setPostcode($director->corporatePostcode);
                $address->setCountry($director->corporateCountryId); /** @phpstan-ignore-line*/
                $directorCorporate->setAddress($address);

                $directorCorporate->setConsentToAct(TRUE);

                $identification = $directorCorporate->getIdentification(Identification::UK);
                $identification->setPlaceRegistered($director->countryRegistered);
                $identification->setRegistrationNumber($director->registrationNumber);
                $directorCorporate->setIdentification($identification);
                $directorCorporate->setNomineeType($director->getNomineeType());

                $directorCorporate->save();
            }
        }
    }

    /**
     * @param CompanyEntity $company
     * @return CompanySummary
     */
    public function getCompanySummaryByCompany(CompanyEntity $company)
    {
        $companyId = $company->getCompanyId();
        $oldCompany = Company::getCompany($companyId);
        $data = $oldCompany->getData();
        return CompanySummary::getCompanySummary($data['company_details'], $data['registered_office'], $data['sail_address'], $data['directors'], $data['secretaries'], $data['shareholders'], $data['capitals']);
    }

    /**
     * @param CompanyEntity $company
     * @return CompanyIncorporationEntity
     */
    public function getLastCompanyIncorporationSubmission(CompanyEntity $company)
    {
        return $this->repository->getLastCompanyIncorporationSubmission($company); /** @phpstan-ignore-line*/
    }

    /**
     * @param CompanyEntity $company
     * @return bool
     */
    public function isCompanyIncorporationPending(CompanyEntity $company)
    {
        $formSubmission = $this->getLastCompanyIncorporationSubmission($company);

        return $formSubmission && $formSubmission->isPending(); /** @phpstan-ignore-line*/
    }

    /**
     * @param CompanyEntity $company
     */
    public function refresh(CompanyEntity $company)
    {
        $this->repository->refresh($company);
    }

    /**
     * @param CustomerEntity $customer
     * @param int $id
     * @throws NoResultException
     * @return CompanyEntity
     */
    public function getCustomerCompanyById(CustomerEntity $customer, $id)
    {
        $company = $this->repository->getCustomerCompany($customer, $id);
        return $company;
    }

    /**
     * @param CompanyEntity $company
     * @return CompanySetting
     */
    public function dismissBankOffer(CompanyEntity $company)
    {
        $company
            ->getSettings()
            ->getBankingOfferDismissed()
            ->dismiss();
        $this->companySettingsRepository->saveEntity($company); /** @phpstan-ignore-line*/
    }

    /**
     * @param CompanyEntity $company
     * @return CompanySetting
     */
    public function dismissBankContactDetails(CompanyEntity $company)
    {
        $company
            ->getSettings()
            ->getBankingContactDetailsDismissed()
            ->dismiss();
        $this->companySettingsRepository->saveEntity($company); /** @phpstan-ignore-line*/
    }

    /**
     * @param CompanyEntity $company
     * @param string $type
     * @param Token $token
     * @return CompanySetting
     */
    public function setRegisteredOfficeUpsellType(CompanyEntity $company, $type, Token $token)
    {
        $setting = $company->getSettings()->getRegisteredOfficeUpsell();
        $setting->setType($type); /** @phpstan-ignore-line*/
        $setting->setTokenId($token->getId()); /** @phpstan-ignore-line*/

        $this->companySettingsRepository->saveEntity($setting); /** @phpstan-ignore-line*/
    }

    public function hasBoughtProduct(CompanyEntity $company, Product $product): bool
    {
        return (bool)$this->orderItemRepository->findFirstInProductIds($company, [$product->getId()]);
    }

    public function saveBusinessPhoneOption(BusinessPhoneOption $businessPhoneOption)
    {
        return $this->repository->saveEntity($businessPhoneOption);
    }

    public function getEligibleCompaniesToEnableAutoRenewal(): array
    {
        return $this->repository->getEligibleCompaniesToEnableAutoRenewal();
    }

    public function clearDoctrineMemory(): void
    {
        $this->repository->getEntityManager()->clear();
        $this->orderItemRepository->getEntityManager()->clear();
        $this->companySettingsRepository->getEntityManager()->clear();
    }
}
