<?php

namespace MailScanModule\Repositories;

use Doctrine\DBAL\Connection;
use MailScanModule\Builders\VoSearchSQLBuilder;
use MailScanModule\Dto\AdvancedQuery;
use MailScanModule\Dto\SearchResult;
use MailScanModule\Dto\SearchResultFactory;

class VoSearchRepository
{
    /**
     * @var Connection
     */
    private $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    public function findCompaniesByName(
        string $serviceStatus,
        ?string $companyName,
        ?AdvancedQuery $companyNameAdvanced = null,
    ): SearchResult {
        $stmt = $this->connection->prepare(
            !empty($companyName)
                ? VoSearchSQLBuilder::prepareQueryForCompaniesByName($companyName)
                : VoSearchSQLBuilder::prepareQueryForCompaniesByName(
                    $companyName,
                    $companyNameAdvanced->getStartsWithWithoutSpecialChars(),
                    $companyNameAdvanced->getContainsWithoutSpecialChars(),
                    $companyNameAdvanced->getEndsWithWithoutSpecialChars()
                )
        );

        $result = $stmt->execute();

        return SearchResultFactory::createFromSqlData(
            $result->fetchAll(),
            $serviceStatus
        );
    }

    public function findCompaniesByLpNumber(
        string $serviceStatus,
        string $lpNumber,
    ): SearchResult {
        $stmt = $this->connection->prepare(
            VoSearchSQLBuilder::prepareQueryForCompaniesByLpNumber($lpNumber)
        );

        $result = $stmt->execute();

        return SearchResultFactory::createFromSqlData(
            $result->fetchAll(),
            $serviceStatus
        );
    }

    public function findOfficersByName(
        string $serviceStatus,
        ?string $officerName = null,
        ?AdvancedQuery $officerNameAdvancedSearch = null,
    ): SearchResult {
        $stmt = $this->connection->prepare(
            !empty($officerName)
                ? VoSearchSQLBuilder::prepareQueryForOfficersByName($officerName)
                : VoSearchSQLBuilder::prepareQueryForOfficersByName(
                    $officerName,
                    $officerNameAdvancedSearch->getStartsWith(),
                    $officerNameAdvancedSearch->getContains(),
                    $officerNameAdvancedSearch->getEndsWith()
                )
        );
        $result = $stmt->execute();

        return SearchResultFactory::createFromSqlData(
            $result->fetchAll(),
            $serviceStatus
        );
    }

    public function findByCompanyNameOfficerName(
        string $serviceStatus,
        ?string $companyName,
        ?string $officerName,
        ?AdvancedQuery $companyNameAdvancedSearch = null,
        ?AdvancedQuery $officerNameAdvancedSearch = null,
    ): SearchResult {
        $companies = $this->findCompaniesByName($companyName, $serviceStatus, $companyNameAdvancedSearch);

        if ($companies->isEmpty()) {
            return SearchResult::empty();
        }

        $stmt = $this->connection->prepare(
            !empty($officerName)
                ? VoSearchSQLBuilder::prepareQueryForOfficersByName(
                    $officerName, null, null, null, $companies->getCompanyIds())
                : VoSearchSQLBuilder::prepareQueryForOfficersByName(
                    $officerName,
                    $officerNameAdvancedSearch->getStartsWith(),
                    $officerNameAdvancedSearch->getContains(),
                    $officerNameAdvancedSearch->getEndsWith(),
                    $companies->getCompanyIds()
                )
        );

        $result = $stmt->execute();

        $people = SearchResultFactory::createFromSqlData($result->fetchAll(), $serviceStatus);

        if ($people->isEmpty()) {
            return SearchResult::empty();
        }

        return $people
            ->union($companies->filterByIds(
                $people->getCompanyIds()));
    }

    public function findByLpNumberOfficerName(
        string $serviceStatus,
        string $lpNumber,
        ?string $officerName = null,
        ?AdvancedQuery $officerNameAdvancedSearch = null,
    ): SearchResult {
        $companies = $this->findCompaniesByLpNumber($serviceStatus, $lpNumber);

        if ($companies->isEmpty()) {
            return SearchResult::empty();
        }

        $stmt = $this->connection->prepare(
            !empty($officerName)
                ? VoSearchSQLBuilder::prepareQueryForOfficersByName(
                    $officerName, null, null, null, $companies->getCompanyIds())
                : VoSearchSQLBuilder::prepareQueryForOfficersByName(
                    $officerName,
                    $officerNameAdvancedSearch->getStartsWith(),
                    $officerNameAdvancedSearch->getContains(),
                    $officerNameAdvancedSearch->getEndsWith(),
                    $companies->getCompanyIds()
                )
        );

        $result = $stmt->execute();

        $people = SearchResultFactory::createFromSqlData($result->fetchAll(), $serviceStatus);

        return $people->union($companies->filterByIds($people->getCompanyIds()));
    }

    public function findCustomersByCustomerEmail(array $customers, string $customerName, string $serviceStatus): SearchResult
    {
        $emailQuery = "''";
        for ($i = 0; $i < count($customers); ++$i) {
            if ($i == 0) {
                $emailQuery = sprintf(" '%s' ", $customers[$i]['email']);
            } else {
                $emailQuery .= sprintf(" OR '%s'", $customers[$i]['email']);
            }
        }

        $stmt = $this->connection->prepare(
            VoSearchSQLBuilder::prepareQueryForCustomersByCustomerEmail($emailQuery, $customerName)
        );

        $result = $stmt->execute();

        return SearchResultFactory::createFromSqlData($result->fetchAll(), $serviceStatus);
    }
}
