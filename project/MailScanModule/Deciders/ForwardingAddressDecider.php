<?php

declare(strict_types=1);

namespace MailScanModule\Deciders;

use CompanyModule\Entities\Settings\PostItemHandlingSetting;
use CompanyModule\Facades\PostItemHandlingFacade;
use Doctrine\ORM\NonUniqueResultException;
use Entities\Company;
use Exceptions\Technical\NodeException;
use MailScanModule\Exceptions\InvalidForwardingAddressException;
use MailScanModule\Exceptions\NotAMailboxProductException;
use MailScanModule\Facades\MailForwardingAddressFacade;
use MailScanModule\Services\MailboxService;

class ForwardingAddressDecider
{

    public function __construct(
        private MailForwardingAddressFacade $mailForwardingAddressFacade,
        private PostItemHandlingFacade $postItemHandlingFacade,
        private MailboxService $mailboxService,
    ) {
    }

    /**
     * @throws NotAMailboxProductException
     * @throws InvalidForwardingAddressException
     * @throws NonUniqueResultException
     * @throws NodeException
     * @throws \Exception
     */
    public function companyNeedsMailForwardingAddress(Company $company): bool
    {
        $address = $this->mailForwardingAddressFacade->getMailForwardingAddressSetting($company);
        $handlingSettings = $this->postItemHandlingFacade->getPostItemHandlingSetting($company);

        $postItemsSettings = $handlingSettings?->getHandlingSettingByType(PostItemHandlingSetting::KEY_POST_ITEM_HANDLING);
        $parcelsSettings = $handlingSettings?->getHandlingSettingByType(PostItemHandlingSetting::KEY_PARCEL_HANDLING);
        $tier = $this->mailboxService->getMailBoxTierFromCompany($company);

        if (
            (
                $postItemsSettings === PostItemHandlingSetting::VALUE_ITEM_SCAN_AND_POST
                || $parcelsSettings === PostItemHandlingSetting::VALUE_PARCEL_POST
            )
            && !$address->isValid()
            && !is_null($tier)
        ) {
            return true;
        }

        return false;
    }
}
