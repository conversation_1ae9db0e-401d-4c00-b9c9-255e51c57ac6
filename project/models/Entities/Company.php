<?php

namespace Entities;

use Models\Products\AnnualReturn;
use CompaniesHouseModule\Entities\Address;
use CompaniesHouseModule\Entities\IAddress;
use CompanyModule\Domain\Company\CompanyName;
use CompanyModule\Entities\Settings\CompanySetting;
use CompanyModule\Entities\Settings\CompanySettingList;
use CompanyModule\ICompany;
use CustomerModule\Entities\BusinessPhoneOption;
use CustomerModule\Entities\Settings\PaymentGatewaySetting;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as Orm;
use Entities\CompanyHouse\FormSubmission;
use Entities\Register\Member;
use function FunctionalModule\Helpers\Object\filter;
use function FunctionalModule\Helpers\Object\mapToArray;
use const FunctionalModule\Helpers\Object\toArray;
use function FunctionalModule\Transformations\hasValue;
use Gedmo\Mapping\Annotation as Gedmo;
use MsgDomain\Address\Postcode;
use Models\Products\RegisterOffice;
use ToolkitOfferModule\Entities\CompanyToolkitOffer;
use UserModule\Contracts\ICustomer;
use Utils\Date;
use Models\Products\Package;
use function Widmogrod\Functional\compose;

/**
 * Company
 * @Orm\Entity(repositoryClass = "Repositories\CompanyRepository")
 * @Orm\Table(name="ch_company")
 * @Gedmo\Loggable(logEntryClass="LoggableModule\Entities\LogEntry")
 */
class Company extends EntityAbstract implements ICompany, IAddress
{
    const COMPANY_CATEGORY_BYSHR = 'BYSHR';
    const COMPANY_CATEGORY_BYGUAR = 'BYGUAR';
    const COMPANY_CATEGORY_PLC = 'PLC';
    const COMPANY_CATEGORY_LLP = 'LLP';
    const COMPANY_CATEGORY_EEIG = 'eeig';
    const COMPANY_CATEGORY_UKEIG = 'ukeig';
    const COMPANY_CATEGORY_UK_ESTABLISHMENT = 'uk-establishment';
    const COMPANY_CATEGORY_LIMITED_PARTNERSHIP = 'limited-partnership';

    const COMPANY_STATUS_DISSOLVED = 'Dissolved';
    const COMPANY_STATUS_ACTIVE = 'Active';
    const COMPANY_STATUS_ACTIVE_STRIKE_OFF = 'Active - Proposal to Strike off';
    const COMPANY_STATUS_OPEN = 'Open';

    const COMPANY_DEFAULT_NAME = 'Please Choose a Company Name Limited';

    /**
     * @var array
     */
    public static $companyCategories = [
        self::COMPANY_CATEGORY_BYSHR => 'Private company limited by shares',
        self::COMPANY_CATEGORY_BYGUAR => 'Private company limited by guarantee',
        self::COMPANY_CATEGORY_PLC => 'Public limited company',
        self::COMPANY_CATEGORY_LLP => 'Limited Liability Partnership',
    ];

    /**
     * @var integer
     *
     * @Orm\Column(name="company_id", type="integer", nullable=false)
     * @Orm\Id
     * @Orm\GeneratedValue(strategy="IDENTITY")
     */
    private $companyId;

    /**
     * @var integer
     *
     * @Orm\Column(name="product_id", type="integer", nullable=true)
     */
    private $productId;

    /**
     * @var boolean
     *
     * @Orm\Column(name="is_certificate_printed", type="boolean", nullable=false)
     */
    private $isCertificatePrinted = FALSE;

    /**
     * @var boolean
     *
     * @Orm\Column(name="is_bronze_cover_letter_printed", type="boolean", nullable=false)
     */
    private $isBronzeCoverLetterPrinted = FALSE;

    /**
     * @var boolean
     *
     * @Orm\Column(name="is_ma_printed", type="boolean", nullable=false)
     */
    private $isMaPrinted = TRUE;

    /**
     * @var boolean
     *
     * @Orm\Column(name="is_ma_cover_letter_printed", type="boolean", nullable=false)
     */
    private $isMaCoverLetterPrinted = TRUE;

    /**
     * @var string
     *
     * @Orm\Column(name="company_name", type="string", length=160, nullable=false)
     * @Gedmo\Versioned
     */
    private $companyName;

    /**
     * @var string
     *
     * @Orm\Column(name="company_number", type="string", length=17, nullable=true)
     */
    private $companyNumber;

    /**
     * @var string
     *
     * @Orm\Column(name="authentication_code", type="string", length=6, nullable=true)
     * @Gedmo\Versioned
     */
    private $authenticationCode;

    /**
     * @var Date|null
     *
     * @Orm\Column(name="incorporation_date", type="date", nullable=true)
     */
    private $incorporationDate;

    /**
     * @var DateTime
     *
     * @Orm\Column(name="dissolution_date", type="date", nullable=true)
     */
    private $dissolutionDate;

    /**
     * @var string
     *
     * @Orm\Column(name="company_category", type="string", length=160, nullable=true)
     */
    private $companyCategory;

    /**
     * @var string
     *
     * @Orm\Column(name="jurisdiction", type="string", length=20, nullable=true)
     */
    private $jurisdiction;

    /**
     * @var \DateTime
     *
     * @Orm\Column(name="made_up_date", type="date", nullable=true)
     */
    private $madeUpDate;

    /**
     * @var string
     *
     * @Orm\Column(name="premise", type="string", length=50, nullable=true)
     */
    private $premise;

    /**
     * @var string
     *
     * @Orm\Column(name="street", type="string", length=50, nullable=true)
     */
    private $street;

    /**
     * @var string
     *
     * @Orm\Column(name="thoroughfare", type="string", length=50, nullable=true)
     */
    private $thoroughfare;

    /**
     * @var string
     *
     * @Orm\Column(name="post_town", type="string", length=50, nullable=true)
     */
    private $postTown;

    /**
     * @var string
     *
     * @Orm\Column(name="county", type="string", length=50, nullable=true)
     */
    private $county;

    /**
     * @var string
     *
     * @Orm\Column(name="country", type="string", nullable=true)
     */
    private $country;

    /**
     * @var string
     *
     * @TODO sync should use doctrine entity
     *
     * @Orm\Column(name="postcode", type="string", length=15, nullable=true)
     * @Gedmo\Versioned
     */
    private $postcode;

    /**
     * @var string
     *
     * @Orm\Column(name="care_of_name", type="string", length=100, nullable=true)
     */
    private $careOfName;

    /**
     * @var string
     *
     * @Orm\Column(name="po_box", type="string", length=10, nullable=true)
     */
    private $poBox;

    /**
     * @var string
     *
     * @Orm\Column(name="sail_premise", type="string", length=50, nullable=true)
     */
    private $sailPremise;

    /**
     * @var string
     *
     * @Orm\Column(name="sail_street", type="string", length=50, nullable=true)
     */
    private $sailStreet;

    /**
     * @var string
     *
     * @Orm\Column(name="sail_thoroughfare", type="string", length=50, nullable=true)
     */
    private $sailThoroughfare;

    /**
     * @var string
     *
     * @Orm\Column(name="sail_post_town", type="string", length=50, nullable=true)
     */
    private $sailPostTown;

    /**
     * @var string
     *
     * @Orm\Column(name="sail_county", type="string", length=50, nullable=true)
     */
    private $sailCounty;

    /**
     * @var string
     *
     * @Orm\Column(name="sail_country", type="string", nullable=true)
     */
    private $sailCountry;

    /**
     * @var string
     *
     * @Orm\Column(name="sail_postcode", type="string", length=15, nullable=true)
     * @Gedmo\Versioned
     */
    private $sailPostcode;

    /**
     * @var string
     *
     * @Orm\Column(name="sail_care_of_name", type="string", length=100, nullable=true)
     */
    private $sailCareOfName;

    /**
     * @var string
     *
     * @Orm\Column(name="sail_po_box", type="string", length=10, nullable=true)
     */
    private $sailPoBox;

    /**
     * @var string
     *
     * @Orm\Column(name="sic_code1", type="string", length=6, nullable=true)
     */
    private $sicCode1 = '82990';

    /**
     * @var string
     *
     * @Orm\Column(name="sic_code2", type="string", length=6, nullable=true)
     */
    private $sicCode2;

    /**
     * @var string
     *
     * @Orm\Column(name="sic_code3", type="string", length=6, nullable=true)
     */
    private $sicCode3;

    /**
     * @var string
     *
     * @Orm\Column(name="sic_code4", type="string", length=6, nullable=true)
     */
    private $sicCode4;

    /**
     * @var string
     *
     * @Orm\Column(name="sic_description", type="string", length=255, nullable=true)
     */
    private $sicDescription;

    /**
     * @var string
     *
     * @Orm\Column(name="company_status", type="string", length=255, nullable=true)
     * @Gedmo\Versioned
     */
    private $companyStatus;

    /**
     * @var string
     *
     * @Orm\Column(name="country_of_origin", type="string", length=20, nullable=true)
     */
    private $countryOfOrigin;

    /**
     * @var string
     *
     * @Orm\Column(name="accounts_ref_date", type="string", length=10)
     */
    private $accountsRefDate;

    /**
     * @var bool
     *
     * @Orm\Column(name="accounts_overdue", type="boolean")
     */
    private $accountsOverdue;

    /**
     * @var DateTime
     *
     * @Orm\Column(name="accounts_next_period_start_date", type="date")
     */
    private $accountsNextPeriodStartDate;

    /**
     * @var DateTime
     *
     * @Orm\Column(name="accounts_next_period_end_date", type="date")
     */
    private $accountsNextPeriodEndDate;

    /**
     * @var DateTime
     *
     * @Orm\Column(name="accounts_next_due_date", type="date")
     */
    private $accountsNextDueDate;

    /**
     * @var string
     *
     * @Orm\Column(name="accounts_last_type", type="string")
     */
    private $accountsLastType;

    /**
     * @var DateTime
     *
     * @Orm\Column(name="accounts_last_period_start_date", type="date")
     */
    private $accountsLastPeriodStartDate;

    /**
     * @var DateTime
     *
     * @Orm\Column(name="accounts_last_made_up_date", type="date")
     */
    private $accountsLastMadeUpDate;

    /**
     * @var DateTime
     *
     * @Orm\Column(name="returns_next_made_up_date", type="date")
     */
    private $returnsNextMakeUpDate;

    /**
     * @var DateTime
     *
     * @Orm\Column(name="returns_next_due_date", type="date")
     */
    private $returnsNextDueDate;

    /**
     * @var DateTime
     *
     * @Orm\Column(name="returns_last_made_up_date", type="date")
     */
    private $returnsLastMadeUpDate;

    /**
     * @var bool
     *
     * @Orm\Column(name="returns_overdue", type="boolean")
     */
    private $returnsOverdue;

    /**
     * @var integer
     *
     * @Orm\Column(name="dca_id", type="integer", nullable=true)
     */
    private $dcaId;

    /**
     * @var integer
     *
     * @Orm\Column(name="registered_office_id", type="integer", nullable=true)
     */
    private $registeredOfficeId;

    /**
     * @var integer
     *
     * @Orm\Column(name="service_address_id", type="integer", nullable=true)
     */
    private $serviceAddressId;

    /**
     * @var integer
     *
     * @Orm\Column(name="nominee_director_id", type="integer", nullable=true)
     */
    private $nomineeDirectorId;

    /**
     * @var integer
     *
     * @Orm\Column(name="nominee_secretary_id", type="integer", nullable=true)
     */
    private $nomineeSecretaryId;

    /**
     * @var integer
     *
     * @Orm\Column(name="nominee_subscriber_id", type="integer", nullable=true)
     */
    private $nomineeSubscriberId;

    /**
     * @var integer
     *
     * @Orm\Column(name="annual_return_id", type="integer", nullable=true)
     */
    private $annualReturnId;

    /**
     * @var integer
     *
     * @Orm\Column(name="change_name_id", type="integer", nullable=true)
     */
    private $changeNameId;

    /**
     * @var int
     *
     * @Orm\Column(name="ereminder_id", type="boolean", nullable=true)
     */
    private $ereminderId;

    /**
     * @var DateTime
     *
     * @Orm\Column(name="document_date", type="date", nullable=true)
     */
    private $documentDate;

    /**
     * @var string
     *
     * @Orm\Column(name="document_id", type="string", length=255, nullable=true)
     */
    private $documentId;

    /**
     * @var DateTime
     *
     * @Orm\Column(name="accepted_date", type="date", nullable=true)
     */
    private $acceptedDate;

    /**
     * @var integer
     *
     * @Orm\Column(name="cash_back_amount", type="integer")
     * @Gedmo\Versioned
     */
    private $cashBackAmount = 0;

    /**
     * @var string
     *
     * @Orm\Column(name="no_psc_reason", type="string")
     */
    private $noPscReason;

    /**
     * @var string
     * @Orm\Column(name="registered_email_address", type="string", nullable=true)
     */
    private $registeredEmailAddress;

    /**
     * @var boolean
     *
     * @Orm\Column(name="locked", type="boolean", nullable=false)
     * @Gedmo\Versioned
     */
    private $locked = FALSE;

    /**
     * @var boolean
     *
     * @Orm\Column(name="hidden", type="boolean", nullable=false)
     * @Gedmo\Versioned
     */
    private $hidden = FALSE;

    /**
     * @var boolean
     *
     * @Orm\Column(name="deleted", type="boolean", nullable=false)
     * @Gedmo\Versioned
     */
    private $deleted = FALSE;

    /**
     * @var string
     * @Orm\Column()
     */
    private $etag;

    /**
     * @var DateTime
     * @Orm\Column(name="dtLastSynced", type="datetime")
     */
    private $dateLastSynced;

    /**
     * @var DateTime
     * @Orm\Column(type="datetime")
     * @Gedmo\Timestampable(on="create")
     */
    private $dtc;

    /**
     * @var DateTime
     * @Orm\Column(type="datetime")
     * @Gedmo\Timestampable(on="create", on="update")
     */
    private $dtm;

    /**
     * @var array<Service>
     * @Orm\OneToMany(targetEntity = "Service", mappedBy = "company", cascade = {"persist"})
     * @Orm\JoinColumn(name="company_id", referencedColumnName="companyId")
     */
    private $services;

    /**
     * @var Customer
     * @Orm\ManyToOne(targetEntity="Customer", cascade={"persist"}, inversedBy="companies", fetch="EAGER")
     * @Orm\JoinColumn(name="customer_id", referencedColumnName="customerId")
     */
    private $customer;

    /**
     * @var Order
     * @Orm\OneToOne(targetEntity="Order", cascade={"persist"})
     * @Orm\JoinColumn(name="order_id", referencedColumnName="orderId")
     */
    private $order;

    /**
     * @var ArrayCollection|FormSubmission[]
     * @Orm\OneToMany(targetEntity = "Entities\CompanyHouse\FormSubmission", mappedBy = "company")
     * @Orm\OrderBy({"formSubmissionId" = "DESC"})
     */
    private $formSubmissions;

    /**
     * @var Member[]
     *
     * @Orm\OneToMany(targetEntity="Entities\Register\Member", mappedBy="company", cascade={"persist"}, orphanRemoval=true)
     */
    private $registerMembers;

    /**
     * @var ArrayCollection|CompanyToolkitOffer[]
     *
     * @ORM\OneToMany(targetEntity="ToolkitOfferModule\Entities\CompanyToolkitOffer", mappedBy="company", cascade={"persist"}, orphanRemoval=true)
     */
    private $toolkitOffers;

    /**
     * @var CompanySetting[]
     * @Orm\OneToMany(targetEntity="CompanyModule\Entities\Settings\CompanySetting", mappedBy="company", cascade={"persist"})
     */
    private $settings;

    /**
     * @var BusinessPhoneOption|null
     * @Orm\OneToOne(targetEntity="CustomerModule\Entities\BusinessPhoneOption", mappedBy="company")
     */
    private $businessPhoneOption;


    /**
     * @param Customer $customer
     * @param $companyName
     */
    public function __construct(Customer $customer, $companyName)
    {
        $this->services = new ArrayCollection();
        $this->formSubmissions = new ArrayCollection();
        $this->registerMembers = new ArrayCollection();
        $this->toolkitOffers = new ArrayCollection();
        $this->settings = new ArrayCollection();

        $this->setCustomer($customer);
        $this->setCompanyName($companyName);
    }

    public static function incorporated(
        Customer $customer,
        string $companyNumber,
        string $companyName,
        Date $incorporationDate
    )
    {
        $self = new self($customer, $companyName);
        $self->companyNumber = $companyNumber;
        $self->incorporationDate = $incorporationDate;
        return $self;
    }

    public function getId(): ?int
    {
        return $this->companyId;
    }

    public function setId(int $id): Company
    {
        $this->companyId = $id;
        return $this;
    }



    /**
     * @return integer
     */
    public function getCompanyId()
    {
        return $this->companyId;
    }

    /**
     * @return bool
     */
    public function isIncorporated(): bool
    {
        return !empty($this->companyNumber);
    }

    /**
     * @return bool
     * @deprecated use CompanyFormationModule\Repositories\FormSubmissionRepository
     */
    public function isImported()
    {
        return $this->getIncorporationFormSubmission() ? FALSE : TRUE;
    }

    /**
     * @return integer
     */
    public function getProductId()
    {
        return $this->productId;
    }

    /**
     * @param integer $productId
     */
    public function setProductId($productId)
    {
        $this->productId = $productId;
    }

    /**
     * @return boolean
     */
    public function getIsCertificatePrinted()
    {
        return $this->isCertificatePrinted;
    }

    /**
     * @param boolean $isCertificatePrinted
     */
    public function setIsCertificatePrinted($isCertificatePrinted)
    {
        $this->isCertificatePrinted = $isCertificatePrinted;
    }

    /**
     * @return boolean
     */
    public function getIsBronzeCoverLetterPrinted()
    {
        return $this->isBronzeCoverLetterPrinted;
    }

    /**
     * @param boolean $isBronzeCoverLetterPrinted
     */
    public function setIsBronzeCoverLetterPrinted($isBronzeCoverLetterPrinted)
    {
        $this->isBronzeCoverLetterPrinted = $isBronzeCoverLetterPrinted;
    }

    /**
     * @return boolean
     */
    public function getIsMaPrinted()
    {
        return $this->isMaPrinted;
    }

    /**
     * @param boolean $isMaPrinted
     */
    public function setIsMaPrinted($isMaPrinted)
    {
        $this->isMaPrinted = $isMaPrinted;
    }

    /**
     * @return boolean
     */
    public function getIsMaCoverLetterPrinted()
    {
        return $this->isMaCoverLetterPrinted;
    }

    /**
     * @param boolean $isMaCoverLetterPrinted
     */
    public function setIsMaCoverLetterPrinted($isMaCoverLetterPrinted)
    {
        $this->isMaCoverLetterPrinted = $isMaCoverLetterPrinted;
    }

    /**
     * @return string
     */
    public function getCompanyName()
    {
        return $this->companyName;
    }

    /**
     * @param string $companyName
     */
    public function setCompanyName($companyName)
    {
        $this->companyName = $companyName;
    }

    /**
     * @return string
     */
    public function getCompanyNumber()
    {
        return $this->companyNumber;
    }

    /**
     * @param string $companyNumber
     */
    public function setCompanyNumber($companyNumber)
    {
        $this->companyNumber = (string)$companyNumber;
    }

    /**
     * @return string
     */
    public function getAuthenticationCode()
    {
        return $this->authenticationCode;
    }

    /**
     * @param string|null $authenticationCode
     */
    public function setAuthenticationCode($authenticationCode)
    {
        $this->authenticationCode = $authenticationCode;
    }

    public function getIncorporationDate(): ?Date
    {
        return $this->incorporationDate;
    }

    public function setIncorporationDate(Date $incorporationDate)
    {
        $this->incorporationDate = $incorporationDate;
    }

    public function getDissolutionDate()
    {
        return $this->dissolutionDate;
    }

    public function setDissolutionDate(DateTime $dissolutionDate = NULL)
    {
        $this->dissolutionDate = $dissolutionDate;
    }

    /**
     * @return string
     */
    public function getCompanyCategory()
    {
        return $this->companyCategory;
    }

    /**
     * @param string $companyCategory
     */
    public function setCompanyCategory($companyCategory)
    {
        $this->companyCategory = $companyCategory;
    }

    /**
     * @return string
     */
    public function getJurisdiction()
    {
        return $this->jurisdiction;
    }

    /**
     * @param string $jurisdiction
     */
    public function setJurisdiction($jurisdiction)
    {
        $this->jurisdiction = $jurisdiction;
    }

    /**
     * @return DateTime
     */
    public function getMadeUpDate(): DateTime
    {
        return $this->madeUpDate;
    }

    /**
     * @param DateTime $madeUpDate
     */
    public function setMadeUpDate(DateTime $madeUpDate)
    {
        $this->madeUpDate = $madeUpDate;
    }

    /**
     * @return string
     */
    public function getPremise(): ?string
    {
        return $this->premise;
    }

    /**
     * @param string $premise
     */
    public function setPremise($premise)
    {
        $this->premise = $premise;
    }

    /**
     * @return string
     */
    public function getStreet(): ?string
    {
        return $this->street;
    }

    /**
     * @param string $street
     */
    public function setStreet($street)
    {
        $this->street = $street;
    }

    /**
     * @return string
     */
    public function getThoroughfare(): ?string
    {
        return $this->thoroughfare;
    }

    /**
     * @param string $thoroughfare
     */
    public function setThoroughfare($thoroughfare)
    {
        $this->thoroughfare = $thoroughfare;
    }

    /**
     * @return string
     */
    public function getPostTown(): ?string
    {
        return $this->postTown;
    }

    /**
     * @param string $postTown
     */
    public function setPostTown($postTown)
    {
        $this->postTown = $postTown;
    }

    /**
     * @return string
     */
    public function getCounty(): ?string
    {
        return $this->county;
    }

    /**
     * @param string $county
     */
    public function setCounty($county)
    {
        $this->county = $county;
    }

    /**
     * @return string
     */
    public function getCountry(): ?string
    {
        return $this->country;
    }

    /**
     * @param string $country
     */
    public function setCountry($country)
    {
        $this->country = $country;
    }

    /**
     * @return string
     */
    public function getPostcode(): ?string
    {
        return $this->postcode;
    }

    /**
     * @param string $postcode
     */
    public function setPostcode($postcode)
    {
        $this->postcode = $postcode;
    }

    /**
     * @return string
     */
    public function getCareOfName()
    {
        return $this->careOfName;
    }

    /**
     * @param string $careOfName
     */
    public function setCareOfName($careOfName)
    {
        $this->careOfName = (string)$careOfName;
    }

    /**
     * @return string
     */
    public function getPoBox()
    {
        return $this->poBox;
    }

    /**
     * @param string $poBox
     */
    public function setPoBox($poBox)
    {
        $this->poBox = (string)$poBox;
    }

    /**
     * @return string
     */
    public function getSailPremise()
    {
        return $this->sailPremise;
    }

    /**
     * @param string $sailPremise
     */
    public function setSailPremise($sailPremise)
    {
        $this->sailPremise = $sailPremise;
    }

    /**
     * @return string
     */
    public function getSailStreet()
    {
        return $this->sailStreet;
    }

    /**
     * @param string $sailStreet
     */
    public function setSailStreet($sailStreet)
    {
        $this->sailStreet = $sailStreet;
    }

    /**
     * @return string
     */
    public function getSailThoroughfare()
    {
        return $this->sailThoroughfare;
    }

    /**
     * @param string $sailThoroughfare
     */
    public function setSailThoroughfare($sailThoroughfare)
    {
        $this->sailThoroughfare = $sailThoroughfare;
    }

    /**
     * @return string
     */
    public function getSailPostTown()
    {
        return $this->sailPostTown;
    }

    /**
     * @param string $sailPostTown
     */
    public function setSailPostTown($sailPostTown)
    {
        $this->sailPostTown = $sailPostTown;
    }

    /**
     * @return string
     */
    public function getSailCounty()
    {
        return $this->sailCounty;
    }

    /**
     * @param string $sailCounty
     */
    public function setSailCounty($sailCounty)
    {
        $this->sailCounty = $sailCounty;
    }

    /**
     * @return string
     */
    public function getSailCountry()
    {
        return $this->sailCountry;
    }

    /**
     * @param string $sailCountry
     */
    public function setSailCountry($sailCountry)
    {
        $this->sailCountry = $sailCountry;
    }

    /**
     * @return string
     */
    public function getSailPostcode()
    {
        return $this->sailPostcode;
    }

    /**
     * @param string $sailPostcode
     */
    public function setSailPostcode($sailPostcode)
    {
        $this->sailPostcode = $sailPostcode;
    }

    /**
     * @return string
     */
    public function getSailCareOfName()
    {
        return $this->sailCareOfName;
    }

    /**
     * @param string $sailCareOfName
     */
    public function setSailCareOfName($sailCareOfName)
    {
        $this->sailCareOfName = $sailCareOfName;
    }

    /**
     * @return string
     */
    public function getSailPoBox()
    {
        return $this->sailPoBox;
    }

    /**
     * @param string $sailPoBox
     */
    public function setSailPoBox($sailPoBox)
    {
        $this->sailPoBox = $sailPoBox;
    }

    /**
     * @return string
     */
    public function getSicCode1()
    {
        return $this->sicCode1;
    }

    /**
     * @param string $sicCode1
     */
    public function setSicCode1($sicCode1)
    {
        $this->sicCode1 = $sicCode1;
    }

    /**
     * @return string
     */
    public function getSicCode2()
    {
        return $this->sicCode2;
    }

    /**
     * @param string $sicCode2
     */
    public function setSicCode2($sicCode2)
    {
        $this->sicCode2 = $sicCode2;
    }

    /**
     * @return string
     */
    public function getSicCode3()
    {
        return $this->sicCode3;
    }

    /**
     * @param string $sicCode3
     */
    public function setSicCode3($sicCode3)
    {
        $this->sicCode3 = $sicCode3;
    }

    /**
     * @return string
     */
    public function getSicCode4()
    {
        return $this->sicCode4;
    }

    /**
     * @param string $sicCode4
     */
    public function setSicCode4($sicCode4)
    {
        $this->sicCode4 = $sicCode4;
    }

    /**
     * @return array
     */
    public function getSicCodes()
    {
        return array_filter(
            [
                $this->getSicCode1(),
                $this->getSicCode2(),
                $this->getSicCode3(),
                $this->getSicCode4(),
            ]
        );
    }

    /**
     * @param array $sicCodes
     */
    public function setSicCodes(array $sicCodes)
    {
        $sicCodes = array_pad(array_values(array_unique($sicCodes)), 4, NULL);

        $this->sicCode1 = $sicCodes[0];
        $this->sicCode2 = $sicCodes[1];
        $this->sicCode3 = $sicCodes[2];
        $this->sicCode4 = $sicCodes[3];
    }

    /**
     * @return string
     */
    public function getSicDescription()
    {
        return $this->sicDescription;
    }

    /**
     * @param string $sicDescription
     */
    public function setSicDescription($sicDescription)
    {
        $this->sicDescription = $sicDescription;
    }

    /**
     * @return string
     */
    public function getCompanyStatus()
    {
        return $this->companyStatus;
    }

    /**
     * @return string
     */
    public function getStatus()
    {
        return $this->getCompanyStatus();
    }

    /**
     * @param string $companyStatus
     */
    public function setCompanyStatus($companyStatus)
    {
        $this->companyStatus = (string)$companyStatus;
    }

    public function getComputedStatus()
    {
        // --- company number is set ---
        if ($this->isIncorporated()) {
            return 'complete';
        }

        // --- imported company ---
        if (empty($this->getIncorporationFormSubmission())) {
            return null;
        }

        // --- check form submission status ---
        $lastFormSubmissionStatus = $this->getIncorporationFormSubmission()->getResponse();

        switch ($lastFormSubmissionStatus) {
            case FormSubmission::RESPONSE_PENDING:
            case FormSubmission::RESPONSE_INTERNAL_FAILURE:
            case FormSubmission::RESPONSE_WITHHOLD:
                return 'pending';
            case FormSubmission::RESPONSE_ACCEPT:
                return 'complete';
            case FormSubmission::RESPONSE_REJECT:
                return 'rejected';
            case FormSubmission::RESPONSE_ERROR:
                return 'error';
            default: // NULL
                return 'incomplete';
        }
    }

    /**
     * @return string
     */
    public function getCountryOfOrigin()
    {
        return $this->countryOfOrigin;
    }

    /**
     * @param string $countryOfOrigin
     */
    public function setCountryOfOrigin($countryOfOrigin)
    {
        $this->countryOfOrigin = (string)$countryOfOrigin;
    }

    /**
     * @return string
     */
    public function getAccountsRefDate()
    {
        return $this->accountsRefDate;
    }

    /**
     * @param string $accountsRefDate
     */
    public function setAccountsRefDate($accountsRefDate)
    {
        $this->accountsRefDate = (string)$accountsRefDate;
    }

    public function getAccountsOverdue()
    {
        return $this->accountsOverdue;
    }

    public function setAccountsOverdue(bool $accountsOverdue = NULL)
    {
        $this->accountsOverdue = $accountsOverdue;
    }

    public function getAccountsNextPeriodStartDate()
    {
        return $this->accountsNextPeriodStartDate;
    }

    public function setAccountsNextPeriodStartDate(DateTime $accountsNextPeriodStartDate = NULL)
    {
        $this->accountsNextPeriodStartDate = $accountsNextPeriodStartDate;
    }

    public function getAccountsNextPeriodEndDate()
    {
        return $this->accountsNextPeriodEndDate;
    }

    public function setAccountsNextPeriodEndDate(DateTime $accountsNextPeriodEndDate = NULL)
    {
        $this->accountsNextPeriodEndDate = $accountsNextPeriodEndDate;
    }

    public function getAccountsNextDueDate()
    {
        return $this->accountsNextDueDate;
    }

    public function setAccountsNextDueDate(DateTime $accountsNextDueDate = NULL)
    {
        $this->accountsNextDueDate = $accountsNextDueDate;
    }

    public function getAccountsLastType()
    {
        return $this->accountsLastType;
    }

    public function setAccountsLastType(string $accountsLastType = NULL)
    {
        $this->accountsLastType = $accountsLastType;
    }

    public function getAccountsLastPeriodStartDate()
    {
        return $this->accountsLastPeriodStartDate;
    }

    public function setAccountsLastPeriodStartDate(DateTime $accountsLastPeriodStartDate = NULL)
    {
        $this->accountsLastPeriodStartDate = $accountsLastPeriodStartDate;
    }

    public function getAccountsLastMadeUpDate()
    {
        return $this->accountsLastMadeUpDate;
    }

    public function setAccountsLastMadeUpDate(DateTime $accountsLastMadeUpDate = NULL)
    {
        $this->accountsLastMadeUpDate = $accountsLastMadeUpDate;
    }

    public function getReturnsNextMakeUpDate()
    {
        return $this->returnsNextMakeUpDate;
    }

    public function setReturnsNextMakeUpDate(DateTime $returnsNextMakeUpDate = NULL)
    {
        $this->returnsNextMakeUpDate = $returnsNextMakeUpDate;
    }

    public function getReturnsNextDueDate()
    {
        return $this->returnsNextDueDate;
    }

    public function setReturnsNextDueDate(DateTime $returnsNextDueDate = NULL)
    {
        $this->returnsNextDueDate = $returnsNextDueDate;
    }

    public function getReturnsLastMadeUpDate()
    {
        return $this->returnsLastMadeUpDate;
    }

    public function setReturnsLastMadeUpDate(DateTime $returnsLastMadeUpDate = NULL)
    {
        $this->returnsLastMadeUpDate = $returnsLastMadeUpDate;
    }

    public function getReturnsOverdue()
    {
        return $this->returnsOverdue;
    }

    public function setReturnsOverdue(bool $returnsOverdue = NULL)
    {
        $this->returnsOverdue = $returnsOverdue;
    }

    /**
     * @return integer
     */
    public function getDcaId()
    {
        return $this->dcaId;
    }

    /**
     * @param integer $dcaId
     */
    public function setDcaId($dcaId)
    {
        $this->dcaId = $dcaId;
    }

    /**
     * @return integer
     */
    public function getRegisteredOfficeId()
    {
        return $this->registeredOfficeId;
    }

    /**
     * @param integer $registeredOfficeId
     */
    public function setRegisteredOfficeId($registeredOfficeId)
    {
        $this->registeredOfficeId = $registeredOfficeId;
    }

    /**
     * @return integer
     */
    public function getServiceAddressId()
    {
        return $this->serviceAddressId;
    }

    /**
     * @param integer $serviceAddressId
     */
    public function setServiceAddressId($serviceAddressId)
    {
        $this->serviceAddressId = $serviceAddressId;
    }

    /**
     * @return integer
     */
    public function getNomineeDirectorId()
    {
        return $this->nomineeDirectorId;
    }

    /**
     * @param integer $nomineeDirectorId
     */
    public function setNomineeDirectorId($nomineeDirectorId)
    {
        $this->nomineeDirectorId = $nomineeDirectorId;
    }

    /**
     * @return integer
     */
    public function getNomineeSecretaryId()
    {
        return $this->nomineeSecretaryId;
    }

    /**
     * @param integer $nomineeSecretaryId
     */
    public function setNomineeSecretaryId($nomineeSecretaryId)
    {
        $this->nomineeSecretaryId = $nomineeSecretaryId;
    }

    /**
     * @return integer
     */
    public function getNomineeSubscriberId()
    {
        return $this->nomineeSubscriberId;
    }

    /**
     * @param integer $nomineeSubscriberId
     */
    public function setNomineeSubscriberId($nomineeSubscriberId)
    {
        $this->nomineeSubscriberId = $nomineeSubscriberId;
    }

    /**
     * @return integer
     */
    public function getAnnualReturnId()
    {
        return $this->annualReturnId;
    }

    /**
     * @param integer $annualReturnId
     */
    public function setAnnualReturnId($annualReturnId)
    {
        $this->annualReturnId = $annualReturnId;
    }

    /**
     * @return integer
     */
    public function getChangeNameId()
    {
        return $this->changeNameId;
    }

    /**
     * @param integer $changeNameId
     */
    public function setChangeNameId($changeNameId)
    {
        $this->changeNameId = $changeNameId;
    }

    /**
     * @return integer
     */
    public function getEreminderId()
    {
        return $this->ereminderId;
    }

    /**
     * @param integer $ereminderId
     */
    public function setEreminderId($ereminderId)
    {
        $this->ereminderId = $ereminderId;
    }

    /**
     * @return DateTime
     */
    public function getDocumentDate()
    {
        return $this->documentDate;
    }

    /**
     * @param DateTime $documentDate
     */
    public function setDocumentDate(DateTime $documentDate)
    {
        $this->documentDate = $documentDate;
    }

    /**
     * @return string
     */
    public function getDocumentId()
    {
        return $this->documentId;
    }

    /**
     * @param string $documentId
     */
    public function setDocumentId($documentId)
    {
        $this->documentId = $documentId;
    }

    /**
     * @return DateTime
     */
    public function getAcceptedDate()
    {
        return $this->acceptedDate;
    }

    /**
     * @param DateTime $acceptedDate
     */
    public function setAcceptedDate(DateTime $acceptedDate)
    {
        $this->acceptedDate = $acceptedDate;
    }

    /**
     * @return int
     */
    public function getCashBackAmount()
    {
        return $this->cashBackAmount;
    }

    /**
     * @param int $cashBackAmount
     */
    public function setCashBackAmount($cashBackAmount)
    {
        $this->cashBackAmount = $cashBackAmount;
    }

    /**
     * @return string
     */
    public function getNoPscReason()
    {
        return $this->noPscReason;
    }

    public function hasNoPscReason(): bool
    {
        return !empty($this->getNoPscReason());
    }

    /**
     * @param string $noPscReason
     */
    public function setNoPscReason($noPscReason)
    {
        $this->noPscReason = $noPscReason;
    }

    /**
     * @return boolean
     */
    public function getLocked()
    {
        return $this->locked;
    }

    /**
     * @return bool
     */
    public function isLocked()
    {
        return $this->locked;
    }

    /**
     * @param boolean $locked
     */
    public function setLocked($locked)
    {
        $this->locked = $locked;
    }

    /**
     * @return boolean
     */
    public function getHidden()
    {
        return $this->hidden;
    }

    /**
     * @param boolean $hidden
     */
    public function setHidden($hidden)
    {
        $this->hidden = $hidden;
    }

    /**
     * @return boolean
     */
    public function getDeleted()
    {
        return $this->deleted;
    }

    public function getEtag()
    {
        return $this->etag;
    }

    public function setEtag(string $etag = NULL)
    {
        $this->etag = $etag;
    }

    public function getDateLastSynced()
    {
        return $this->dateLastSynced;
    }

    public function setDateLastSynced(DateTime $dateTime = NULL)
    {
        $this->dateLastSynced = $dateTime;
    }

    /**
     * @param boolean $deleted
     */
    public function setDeleted($deleted)
    {
        $this->deleted = $deleted;
    }

    /**
     * @return DateTime
     */
    public function getDtc()
    {
        return $this->dtc;
    }

    /**
     * @param DateTime $dtc
     */
    public function setDtc(DateTime $dtc)
    {
        $this->dtc = $dtc;
    }

    /**
     * @return DateTime
     */
    public function getDtm()
    {
        return $this->dtm;
    }

    /**
     * @param DateTime $dtm
     */
    public function setDtm(DateTime $dtm)
    {
        $this->dtm = $dtm;
    }

    /**
     * @return Service[]|ArrayCollection
     */
    public function getServices()
    {
        return $this->services;
    }

    /**
     * @return Service[]|ArrayCollection
     */
    public function getGroupedServices()
    {
        return $this->services->filter(
            function (Service $service) {
                return !$service->hasParent();
            }
        );
    }

    /**
     * @return Service[]|ArrayCollection
     */
    public function getEnabledParentServices()
    {
        return $this->getGroupedServices()->filter(
            function (Service $service) {
                return $service->isEnabled();
            }
        );
    }

    /**
     * @param string $typeId
     * @return ArrayCollection|Service[]
     */
    public function getEnabledParentServicesWithType($typeId)
    {
        return $this->getEnabledParentServices()->filter(
            function (Service $service) use ($typeId) {
                return $service->hasChildOfType($typeId) || $service->getServiceTypeId() === $typeId;
            }
        );
    }

    /**
     * @return Service[]|ArrayCollection
     */
    public function getEnabledActivatedParentServices()
    {
        return $this->getGroupedServices()->filter(
            function (Service $service) {
                return $service->isEnabled() && $service->hasDates();
            }
        );
    }

    /**
     * @return Service[]|ArrayCollection
     */
    public function getEnabledNotActivatedParentServices()
    {
        return $this->getGroupedServices()->filter(
            function (Service $service) {
                return $service->isEnabled() && !$service->hasDates();
            }
        );
    }

    public function getActiveCorePackageService(): ?Service
    {
        $services = $this->getEnabledActivatedParentServices();

        $services = $services->getValues();
        usort($services, function (Service $item1, Service $item2) {
            return $item2->getDtExpires() <= $item1->getDtExpires() ? 1 : -1;
        });

        $activeType = null;
        $selectedService = null;
        foreach ($services as $service) {
            if (!$activeType && !$service->isActive()) {
                continue;
            }

            if ($service->isCorePackageType()) {

                if (!$activeType) {
                    $selectedService = $service;
                    $activeType = $service->getServiceTypeId();
                }

                if ($activeType && $activeType == $service->getServiceTypeId()) {
                    $selectedService = $service;
                    $activeType = $service->getServiceTypeId();
                }
            }
        }

        return $selectedService;
    }

    /**
     * @param string $serviceTypeId
     * @return Service|null
     */
    public function getActiveServiceOfType($serviceTypeId)
    {
        $services = $this->getEnabledActivatedParentServices();

        foreach ($services as $service) {
            if (!$service->isActive()) {
                continue;
            }

            if ($service->getServiceTypeId() == $serviceTypeId) {
                return $service;
            } elseif ($service->hasChildOfType($serviceTypeId)) {
                return $service->getChildOfType($serviceTypeId);
            }
        }
    }

    /**
     * @return bool
     */
    public function isUsingMsgPostcodeWithoutService()
    {
        return $this->isUsingMsgRegisteredOffice()
            && !$this->getActiveServiceOfType(Service::TYPE_REGISTERED_OFFICE);

    }

    /**
     * @return bool
     */
    public function isNotUsingMsgPostcodeWithService()
    {
        return !$this->isUsingMsgRegisteredOffice()
            && $this->getActiveServiceOfType(Service::TYPE_REGISTERED_OFFICE);
    }

    /**
     * @param string $serviceTypeId
     * @return bool
     */
    public function hasActiveServiceOfType($serviceTypeId): bool
    {
        return (bool)$this->getActiveServiceOfType($serviceTypeId);
    }

    /**
     * @param string $serviceTypeId
     * @return Service[]
     */
    public function getNotActivatedServicesOfType($serviceTypeId)
    {
        $services = [];

        foreach ($this->getEnabledNotActivatedParentServices() as $service) {
            if ($service->getServiceTypeId() == $serviceTypeId) {
                $services[] = $service;
            } elseif ($service->hasChildOfType($serviceTypeId)) {
                $services[] = $service->getChildOfType($serviceTypeId);
            }
        }

        return $services;
    }

    /**
     * @param string $serviceTypeId
     * @return bool
     */
    public function hasNotActivatedServiceOfType($serviceTypeId)
    {
        return !empty($this->getNotActivatedServicesOfType($serviceTypeId));
    }

    /**
     * @param array<Service> $services
     */
    public function setServices($services)
    {
        return $this->services = $services;
    }

    /**
     * @return Customer
     */
    public function getCustomer(): ICustomer
    {
        return $this->customer;
    }

    /**
     * @param Customer $customer
     */
    public function setCustomer(Customer $customer)
    {
        $this->customer = $customer;
    }

    /**
     * @return Order
     */
    public function getOrder()
    {
        return $this->order;
    }

    /**
     * @param Order $order
     */
    public function setOrder(Order $order)
    {
        $this->order = $order;
    }

    /**
     * @return ArrayCollection|FormSubmission[]
     */
    public function getFormSubmissions()
    {
        return $this->formSubmissions;
    }

    public function getLatestFormSubmissionOfType(string $type, ?bool $rejected = null)
    {
        foreach ($this->formSubmissions as $formSubmission) {
            if ($formSubmission->getFormIdentifier() === $type) {
                if (
                    $rejected === null
                    || ($rejected === true && $formSubmission->isRejected())
                    || ($rejected === false && !$formSubmission->isRejected())
                )
                    return $formSubmission;
            }
        }

        return null;
    }

    /**
     * @return FormSubmission\CompanyIncorporation
     */
    public function getIncorporationFormSubmission()
    {
        return $this->getLatestFormSubmissionOfType(FormSubmission::TYPE_COMPANY_INCORPORATION);
    }

    /**
     * @param FormSubmission $formSubmission
     */
    public function addFormSubmission(FormSubmission $formSubmission)
    {
        $formSubmission->setCompany($this);
        $this->formSubmissions[] = $formSubmission;
    }

    /**
     * @param array<FormSubmission> $formSubmissions
     */
    public function setFormSubmissions($formSubmissions)
    {
        return $this->formSubmissions = $formSubmissions;
    }

    /**
     * @param Company $company
     * @return bool
     */
    public function isEqual(Company $company)
    {
        return ($this->getId() === $company->getId());
    }

    /**
     * @param Service $service
     */
    public function addService(Service $service)
    {
        $service->setCompany($this);
        $this->services[] = $service;
    }

    /**
     * @return bool
     */
    public function isLimitedBySharesType()
    {
        return ($this->getCompanyCategory() === self::COMPANY_CATEGORY_BYSHR);
    }

    /**
     * @return Member[]
     */
    public function getRegisterMembers()
    {
        return $this->registerMembers;
    }

    /**
     * @param Member $registerMember
     */
    public function addRegisterMember(Member $registerMember)
    {
        $registerMember->setCompany($this);
        $this->registerMembers[] = $registerMember;
    }

    /**
     * @return bool
     */
    public function hasServices()
    {
        return $this->getServices()->isEmpty() === FALSE;
    }

    /**
     * @return boolean
     */
    public function hasActiveService()
    {
        foreach ($this->getServices() as $service) {
            if ($service->isActive()) {
                return TRUE;
            }
        }
        return FALSE;
    }

    /**
     * @param Service $service
     * @return bool
     */
    public function hasEqualService(Service $service)
    {
        $companyServices = $this->getServicesByType($service->getServiceTypeId());
        foreach ($companyServices as $companyService) {
            if ($companyService->isEqual($service)) {
                return TRUE;
            }
        }
        return FALSE;
    }

    /**
     * @param $serviceTypeId
     * @return Service[]|ArrayCollection
     */
    public function getServicesByType($serviceTypeId)
    {
        $criteria = Criteria::create()
            ->where(Criteria::expr()->eq("serviceTypeId", $serviceTypeId));

        return $this->getServices()->matching($criteria);
    }

    public function hasServiceByType(string $serviceTypeId): bool
    {
        return $this->getServicesByType($serviceTypeId)->count() > 0;
    }

    /**
     * @param Service $service
     * @return NULL|Service
     */
    public function getLastServiceByType($service)
    {
        $collection = $this->getServicesByType($service->getServiceTypeId());
        /** @var Service $lastService */
        $lastService = NULL;
        foreach ($collection as $oldService) {
            if ($service->getId() != $oldService->getId()) {
                if (!$lastService || ($oldService->isEnabled() && $oldService->getDtExpires() > $lastService->getDtExpires())) {
                    $lastService = $oldService;
                }
            }

        }
        return $lastService;
    }

    public function getLastServiceByServiceTypeId(string $serviceTypId): Service
    {
        $collection = $this->getServicesByType($serviceTypId);
        /** @var Service $lastService */
        $lastService = NULL;
        foreach ($collection as $oldService) {
            if (!$lastService || ($oldService->isEnabled() && $oldService->getDtExpires() > $lastService->getDtExpires())) {
                $lastService = $oldService;
            }
        }
        return $lastService;
    }

    /**
     * Returns whether there is active service after service that was given as parameter
     *
     * @param Service $service
     * @return bool
     */
    public function hasActiveRenewalServiceByType($service)
    {
        $criteria = Criteria::create()
            ->where(Criteria::expr()->eq("serviceTypeId", $service->getServiceTypeId()))
            ->andWhere(Criteria::expr()->gt('dtExpires', $service->getDtExpires()));

        return !$this->getServices()->matching($criteria)->filter(
            function (Service $service) {
                return $service->isActive();
            }
        )->isEmpty();
    }

    /**
     * @param array $types
     * @return Service[]|ArrayCollection
     */
    public function getActiveProductServices(array $types)
    {
        return $this->getGroupedServices()->filter(
            function (Service $service) use ($types) {
                return
                    $service->isProductType()
                    && $service->isActivatedAndNotExpired()
                    && in_array($service->getServiceTypeId(), $types);
            }
        );
    }

    /**
     * @param DateTime $date
     * @return ArrayCollection|Service[]
     */
    public function getNonExpiredServicesOn(DateTime $date)
    {
        return $this->getGroupedServices()->filter(
            function (Service $service) use ($date) {
                return $service->isEnabled() && $service->isExpiringAfter($date);
            }
        );
    }

    /**
     * @param array $types
     * @return Service[]|ArrayCollection
     */
    public function getProductServicesByTypes(array $types)
    {
        return $this->getGroupedServices()->filter(
            function (Service $service) use ($types) {
                return
                    $service->isProductType()
                    && $service->isEnabled()
                    && ($service->isActivatedAndNotExpired() || !$service->hasDates())
                    && in_array($service->getServiceTypeId(), $types);
            }
        );
    }

    /**
     * get last package service which includes service type or get last package by type
     * @param string $type
     * @return Service|null
     */
    public function getLastPackageService($type)
    {
        /** @var Service $lastService */
        $lastService = NULL;
        $collection = $this->getServicesByType($type);
        foreach ($collection as $service) {
            if (!$service->isEnabled()) {
                continue;
            }

            if ($service->hasParent()) {
                $parentService = $service->getParent();
                if (!$parentService->isEnabled()) {
                    continue;
                }
            }

            if ($service->isPackageType() && ($service->isActivatedAndNotExpired() || !$service->hasDates())) {
                $expectedService = $service->hasParent() ? $service->getParent() : $service;
                if (!$lastService || $expectedService->getDtExpires() > $lastService->getDtExpires()) {
                    $lastService = $expectedService;
                }
            }
        }
        return $lastService;
    }

    public function getLastPackageServiceForTypes(array $types): ?Service
    {
        foreach ($types as $type) {
            if ($package = $this->getLastPackageService($type)) {
                return $package;
            }
        }

        return null;
    }

    /**
     * @return bool
     */
    public function hasAuthenticationCode()
    {
        return (bool)$this->getAuthenticationCode();
    }


    /**
     * @return ArrayCollection|CompanyToolkitOffer[]
     */
    public function getSelectedToolkitOffers()
    {
        $criteria = Criteria::create()
            ->where(Criteria::expr()->eq('selected', TRUE));

        return $this->toolkitOffers->matching($criteria);
    }

    /**
     * @return bool
     */
    public function toolkitOffersAlreadyChosen()
    {
        return !$this->toolkitOffers->isEmpty();
    }

    /**
     * @param CompanyToolkitOffer $offer
     */
    public function addToolkitOffer(CompanyToolkitOffer $offer)
    {
        if (!$this->toolkitOffers->contains($offer)) {
            $offer->setCompany($this);
            $this->toolkitOffers->add($offer);
        }
    }

    public function clearToolkitOffers()
    {
        $this->toolkitOffers->clear();
    }

    /**
     * @return bool
     */
    public function isDissolved()
    {
        return $this->companyStatus == self::COMPANY_STATUS_DISSOLVED;
    }

    /**
     * @return bool
     */
    public function belongsToUkCustomer()
    {
        return $this->customer->isUkCustomer();
    }

    /**
     * @return bool
     */
    public function canUseOurServiceAddress()
    {
        return $this->hasActiveServiceOfType(Service::TYPE_REGISTERED_OFFICE)
            || $this->hasNotActivatedServiceOfType(Service::TYPE_REGISTERED_OFFICE);
    }

    public function hasAnActiveMailForwardingService(): bool
    {
        return $this->hasActiveServiceOfType(Service::TYPE_SERVICE_ADDRESS)
            || $this->hasActiveServiceOfType(Service::TYPE_REGISTERED_OFFICE);
    }

    /**
     * @param int $serviceTypeId
     * @return Service|null
     */
    public function getCurrentServiceOfType($serviceTypeId)
    {
        $activeServiceAddress = $this->getActiveServiceOfType($serviceTypeId);
        if ($activeServiceAddress) {
            return $activeServiceAddress;
        }

        $notActivatedServiceAddress = $this->getNotActivatedServicesOfType($serviceTypeId);
        if (!empty($notActivatedServiceAddress)) {
            return $notActivatedServiceAddress[0];
        }
    }

    /**
     * @return bool
     */
    public function isDeleted()
    {
        return $this->getDeleted();
    }

    /**
     * @return CompanySettingList
     * @deprecated use CompanyModule\Services\CompanySettingService
     *
     */
    public function getSettings()
    {
        return new CompanySettingList($this, $this->settings->toArray());
    }

    /**
     * @param CompanySetting $setting
     */
    public function addSetting(CompanySetting $setting)
    {
        $this->settings[] = $setting;
    }

    /**
     * @return bool
     */
    public function belongsToWholesale()
    {
        return $this->getCustomer()->isWholesale();
    }

    /**
     * @return bool
     */
    public function isByGuarType()
    {
        return ($this->getCompanyCategory() === self::COMPANY_CATEGORY_BYGUAR);
    }

    /**
     * @return bool
     */
    public function isLlpType()
    {
        return ($this->getCompanyCategory() === self::COMPANY_CATEGORY_LLP);
    }

    /**
     * @return Address
     */
    public function getRegisteredOffice()
    {
        $address = new Address();
        $address->setPremise($this->getPremise());
        $address->setStreet($this->getStreet());
        $address->setThoroughfare($this->getThoroughfare());
        $address->setPostcode($this->getPostcode());
        $address->setPostTown($this->getPostTown());
        $address->setCounty($this->getCounty());
        $address->setCountry($this->getCountry());
        return $address;
    }

    public function setRegisteredOffice(Address $address): void
    {
        $this->setPremise($address->getPremise());
        $this->setStreet($address->getStreet());
        $this->setThoroughfare($address->getThoroughfare());
        $this->setPostcode($address->getPostcode());
        $this->setPostTown($address->getPostTown());
        $this->setCounty($address->getCounty());
        $this->setCountry($address->getCountry());
    }

    /**
     * @return bool
     */
    public function isActive(): bool
    {
        return in_array($this->companyStatus, [self::COMPANY_STATUS_ACTIVE, self::COMPANY_STATUS_ACTIVE_STRIKE_OFF], TRUE);
    }

    /**
     * @return bool
     */
    public function isUsingMsgRegisteredOffice(): bool
    {
        if ($this->isIncorporated()) {
            $postcode = new Postcode(RegisterOffice::POSTCODE);
            return $postcode->isMatching($this->getPostcode());
        } else {
            $companyIncorporation = $this->getIncorporationFormSubmission();
            return $companyIncorporation ? $companyIncorporation->isUsingMsgRegisteredOffice() : FALSE;
        }
    }

    public function getName(): CompanyName
    {
        return CompanyName::uppercased($this->companyName);
    }

    public function getNumber(): ?string
    {
        return $this->companyNumber;
    }

    public function getCustomerEmail(): string
    {
        return $this->customer->getEmail();
    }

    public function markAsDeleted()
    {
        $this->deleted = TRUE;
        if ($this->companyNumber) {
            $this->companyNumber = sprintf('%s_%s', 'DELETED', $this->companyNumber);
            // timestamp does not fit
            //$this->companyNumber = sprintf('%s_%s_%s', 'DELETED', time(), $this->companyNumber);
        }
    }

    public function getServicesByIds(array $services): array
    {
        $ids = mapToArray('intval', $services);
        return compose(
            toArray,
            filter(function (Service $service) use ($ids) {
                return hasValue($service->getId(), $ids);
            })
        )($this->services);
    }

    public function hasNominee(): bool
    {
        return $this->getNomineeDirectorId()
            || $this->getNomineeSecretaryId()
            || $this->getNomineeSubscriberId();
    }

    public function hasConfirmationStatementServiceActive(): bool
    {
        if ($service = $this->getActiveServiceOfType(Service::TYPE_CONFIRMATION_STATEMEMT)) {
            if (
                in_array(
                    $service->getProductId(),
                    [
                        AnnualReturn::ANNUAL_RETURN_SERVICE_PRODUCT,
                        AnnualReturn::ANNUAL_RETURN_EXPRESS_SERVICE_PRODUCT
                    ],
                    true
                )
            ) {
                return true;
            }
        }

        return false;
    }

    public function isEeig(): bool
    {
        return $this->companyCategory === self::COMPANY_CATEGORY_EEIG || $this->companyCategory === self::COMPANY_CATEGORY_UKEIG;
    }

    public function isUkEstablishment(): bool
    {
        return $this->companyCategory === self::COMPANY_CATEGORY_UK_ESTABLISHMENT;
    }

    public function isLimitedPartnership(): bool
    {
        return $this->companyCategory === self::COMPANY_CATEGORY_LIMITED_PARTNERSHIP;
    }

    public function getBusinessPhoneOption(): ?BusinessPhoneOption
    {
        return $this->businessPhoneOption;
    }

    public function setBusinessPhoneOption(?BusinessPhoneOption $businessPhoneOption): void
    {
        $this->businessPhoneOption = $businessPhoneOption;
    }

    public function getPaymentGateway(): ?string
    {
        return $this->customer->getSetting(PaymentGatewaySetting::class)->getPaymentGateway();
    }

    public function getRegisteredEmailAddress(): ?string
    {
        return $this->registeredEmailAddress;
    }

    public function setRegisteredEmailAddress(string $registeredEmailAddress): void
    {
        $this->registeredEmailAddress = $registeredEmailAddress;
    }

    public function hasMainFields(): bool
    {
        // TODO: can premise be 0 ?
        return !empty($this->country) && (!empty($this->premise) || $this->premise === '0');
    }

    public function isBasicPackage(): bool
    {
        return $this->getProductId() == Package::PACKAGE_BASIC;
    }

    public function jsonSerialize(): array
    {
        return [
            'companyId' => $this->companyId,
            'productId' => $this->productId,
            'isCertificatePrinted' => $this->isCertificatePrinted,
            'isBronzeCoverLetterPrinted' => $this->isBronzeCoverLetterPrinted,
            'isMaPrinted' => $this->isMaPrinted,
            'isMaCoverLetterPrinted' => $this->isMaCoverLetterPrinted,
            'companyName' => $this->companyName,
            'companyNumber' => $this->companyNumber,
            'authenticationCode' => $this->authenticationCode,
            'incorporationDate' => $this->incorporationDate,
            'dissolutionDate' => $this->dissolutionDate,
            'companyCategory' => $this->companyCategory,
            'jurisdiction' => $this->jurisdiction,
            'madeUpDate' => $this->madeUpDate,
            'premise' => $this->premise,
            'street' => $this->street,
            'thoroughfare' => $this->thoroughfare,
            'postTown' => $this->postTown,
            'county' => $this->county,
            'country' => $this->country,
            'postcode' => $this->postcode,
            'careOfName' => $this->careOfName,
            'poBox' => $this->poBox,
            'sailPremise' => $this->sailPremise,
            'sailStreet' => $this->sailStreet,
            'sailThoroughfare' => $this->sailThoroughfare,
            'sailPostTown' => $this->sailPostTown,
            'sailCounty' => $this->sailCounty,
            'sailCountry' => $this->sailCountry,
            'sailPostcode' => $this->sailPostcode,
            'sailCareOfName' => $this->sailCareOfName,
            'sailPoBox' => $this->sailPoBox,
            'sicCode1' => $this->sicCode1,
            'sicCode2' => $this->sicCode2,
            'sicCode3' => $this->sicCode3,
            'sicCode4' => $this->sicCode4,
            'sicDescription' => $this->sicDescription,
            'companyStatus' => $this->companyStatus,
            'countryOfOrigin' => $this->countryOfOrigin,
            'accountsRefDate' => $this->accountsRefDate,
            'accountsOverdue' => $this->accountsOverdue,
            'accountsNextPeriodStartDate' => $this->accountsNextPeriodStartDate,
            'accountsNextPeriodEndDate' => $this->accountsNextPeriodEndDate,
            'accountsNextDueDate' => $this->accountsNextDueDate,
            'accountsLastType' => $this->accountsLastType,
            'accountsLastPeriodStartDate' => $this->accountsLastPeriodStartDate,
            'accountsLastMadeUpDate' => $this->accountsLastMadeUpDate,
            'returnsNextMakeUpDate' => $this->returnsNextMakeUpDate,
            'returnsNextDueDate' => $this->returnsNextDueDate,
            'returnsLastMadeUpDate' => $this->returnsLastMadeUpDate,
            'returnsOverdue' => $this->returnsOverdue,
            'dcaId' => $this->dcaId,
            'registeredOfficeId' => $this->registeredOfficeId,
            'serviceAddressId' => $this->serviceAddressId,
            'nomineeDirectorId' => $this->nomineeDirectorId,
            'nomineeSecretaryId' => $this->nomineeSecretaryId,
            'nomineeSubscriberId' => $this->nomineeSubscriberId,
            'annualReturnId' => $this->annualReturnId,
            'changeNameId' => $this->changeNameId,
            'ereminderId' => $this->ereminderId,
            'documentDate' => $this->documentDate,
            'documentId' => $this->documentId,
            'acceptedDate' => $this->acceptedDate,
            'cashBackAmount' => $this->cashBackAmount,
            'noPscReason' => $this->noPscReason,
            'registeredEmailAddress' => $this->registeredEmailAddress,
            'locked' => $this->locked,
            'hidden' => $this->hidden,
            'deleted' => $this->deleted,
            'etag' => $this->etag,
            'dateLastSynced' => $this->dateLastSynced,
            'dtc' => $this->dtc,
            'dtm' => $this->dtm,
            'services' => $this->services,
            'customer' => $this->customer,
            'order' => $this->order,
            'formSubmissions' => $this->formSubmissions,
            'registerMembers' => $this->registerMembers,
            'toolkitOffers' => $this->toolkitOffers,
            'settings' => $this->settings,
            'businessPhoneOption' => $this->businessPhoneOption,
        ];
    }
    public function getSimplifiedCompanyData(): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'status' => $this->getStatus(),
            'category' => $this->getCompanyCategory(),
            'isBasicPackageCompany' => $this->isBasicPackage()
        ];
    }

    public function getActiveOrLatestMailboxService(): ?Service
    {
        return $this->getActiveMailboxService() ?? $this->getLatestMailboxService();
    }

    /**
     * This method is used to get the latest mailbox service, ACTIVE OR NOT.
     */
    public function getLatestMailboxService(): ?Service
    {
        $services = $this->getServices();
        $services = $services->getValues();

        $services = array_filter($services, function (Service $service) {
            return $service->isMailboxService() && !is_null($service->getDtExpiresWithParentCheck());
        });

        $latestService = null;
        foreach ($services as $service) {
            if (
                !$latestService
                || $service->getDtExpiresWithParentCheck() > $latestService->getDtExpiresWithParentCheck()
            ) {
                $latestService = $service;
            }
        }

        return $latestService;
    }

    public function getActiveMailboxService(): ?Service
    {
        $businessService = $this->getActiveMailboxBusinessService();
        if ($businessService) {
            return $businessService;
        }

        $premiumService = $this->getActiveMailboxPremiumService();
        if ($premiumService) {
            return $premiumService;
        }

        $standardService = $this->getActiveMailboxStandardService();
        if ($standardService) {
            return $standardService;
        }

        return $this->getActiveMailboxLiteService();
    }

    public function hasActiveMailboxService(): bool
    {
        return !empty($this->getActiveMailboxService());
    }

    public function getInactiveMailboxService(): ?Service
    {
        /** @var Service $service */
        foreach ($this->getServices() as $service) {
            if (!$service->isActive() && $service->isRegisteredOfficeService()) {
                return $service;
            }
        }

        return null;
    }

    public function hasInactiveMailboxService(): bool
    {
        return !empty($this->getInactiveMailboxService());
    }

    public function hasMailBoxService(): bool
    {
        foreach ($this->getServices() as $service) {
            if ($service->isMailboxService()) {
                return true;
            }
        }

        return false;
    }

    private function getActiveMailboxBusinessService(): ?Service
    {
        foreach ($this->getServices() as $service) {
            if ($service->isActive() && !$service->isOverdueAndExpired() && $service->isMailboxBusinessAddressService()) {
                return $service;
            }
        }

        return null;
    }

    private function getActiveMailboxPremiumService(): ?Service
    {
        foreach ($this->getServices() as $service) {
            if ($service->isActive() && !$service->isOverdueAndExpired() && $service->isMailboxPremiumService()) {
                return $service;
            }
        }

        return null;
    }

    private function getActiveMailboxStandardService(): ?Service
    {
        /** @var Service $service */
        foreach ($this->getServices() as $service) {
            if ($service->isActive() && !$service->isOverdueAndExpired() && $service->isMailboxStandardService()) {
                return $service;
            }
        }

        return null;
    }

    private function getActiveMailboxLiteService(): ?Service
    {
        /** @var Service $service */
        foreach ($this->getServices() as $service) {
            if ($service->isActive() && !$service->isOverdueAndExpired() && $service->isRegisteredOfficeService()) {
                return $service;
            }
        }

        return null;
    }
}
