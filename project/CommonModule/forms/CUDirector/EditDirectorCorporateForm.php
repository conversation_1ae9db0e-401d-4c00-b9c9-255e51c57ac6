<?php

namespace CommonModule\forms\CUDirector;

use AdminModule\forms\CHForm;
use CompaniesHouse\Repositories\CountriesRepository;
use Framework\Forms\Controls\SingleCheckbox;
use Libs\CHFiling\Core\Company;
use Framework\FControler;
use Libs\CHFiling\Core\UtilityClass\CorporateDirector;
use Framework\Forms\FForm;
use Libs\Forms\Helpers\Prefiller;
use Libs\Forms\Validators\CHValidator;

class EditDirectorCorporateForm extends CHForm
{

    /**
     * @var FControler 
     */
    public $controller;

    /**
     * @var Company 
     */
    public $company;

    /**
     * @var CorporateDirector 
     */
    public $director;

    /**
     * @var Prefiller 
     */
    public $prefiller;

    /**
     * @var array 
     */
    public $callback;

    /**
     * @var CountriesRepository
     */
    private $chCountriesRepository;

    public function startup(
        FControler $controller,
        array $callback,
        Company $company,
        CorporateDirector $director,
        Prefiller $prefiller,
        CountriesRepository $chCountriesRepository
    )
    {
        $this->controller = $controller;
        $this->company = $company;
        $this->director = $director;
        $this->prefiller = $prefiller;
        $this->callback = $callback;
        $this->chCountriesRepository = $chCountriesRepository;
        $this->init();
    }

    private function init()
    {
        $prefillAddress = $this->prefiller->getPrefillAdresses();
        if ($this->company->getType() == 'LLP') {
            $formBlocks = array('changeName', 'changeAddress', 'changeUK', 'changeDesignation');
        } else {
            $formBlocks = array('changeName', 'changeAddress', 'changeUK');
        }

        // Name
        $this->addFieldset('Person');
        $this->addCheckbox('changeName', 'Change:', 1)
            ->addRule(array(CHValidator::class, 'Validator_changeChecboxRequired'), 'No changes have been made', $formBlocks)
            ->addAtrib('class', 'isChange');
        $this->addText('corporate_name', 'Company name*')
            ->class('form-control')
            ->addRule(array(CHValidator::class, 'Validator_directorEditChangeRequired'), 'Please provide company name', 'changeName');

        // prefill
        $this->addFieldset('Prefill');
        $this->addSelect('prefillAddress', 'Prefill Address', $prefillAddress['select'])
            ->class('form-control')
            ->setFirstOption('--- Select ---');

        // adddress
        $this->addFieldset('Address');
        $this->addCheckbox('changeAddress', 'Change:', 1)->addAtrib('class', 'isChange');
        $this->addText('premise', 'Building name/number *')
            ->class('form-control')
            ->addRule(array(CHValidator::class, 'Validator_directorEditChangeRequired'), 'Please provide Building name/number', 'changeAddress')
            ->addRule(FForm::MAX_LENGTH, "Building name/number can't be more than 50 characters", 50);
        $this->addText('street', 'Street *')
            ->class('form-control')
            ->addRule(array(CHValidator::class, 'Validator_directorEditChangeRequired'), 'Please provide Street', 'changeAddress')
            ->addRule(FForm::MAX_LENGTH, "Street can't be more than 50 characters", 50);
        $this->addText('thoroughfare', 'Address 3')
            ->class('form-control')
            ->addRule(FForm::MAX_LENGTH, "Address 3 can't be more than 50 characters", 50);
        $this->addText('post_town', 'Town *')
            ->class('form-control')
            ->addRule(array(CHValidator::class, 'Validator_directorEditChangeRequired'), 'Please provide Town', 'changeAddress')
            ->addRule(FForm::MAX_LENGTH, "Town can't be more than 50 characters", 50);
        $this->addText('county', 'County')
            ->class('form-control')
            ->addRule(FForm::MAX_LENGTH, "County can't be more than 50 characters", 50);
        $this->addText('postcode', 'Postcode *')
            ->class('form-control')
            ->addRule(array(CHValidator::class, 'Validator_directorEditChangeRequired'), 'Please provide Postcode', 'changeAddress')
            ->addRule(FForm::MAX_LENGTH, "Postcode can't be more than 15 characters", 15);
        $this->addSelect('country', 'Country *', $this->chCountriesRepository->getServiceAddressCountries())
            ->class('form-control')
            ->setFirstOption('--- Select a country ---')
            ->addRule(array(CHValidator::class, 'Validator_directorEditChangeRequired'), 'Please provide Country', 'changeAddress')
            ->addRule(FForm::MAX_LENGTH, "Country can't be more than 50 characters", 50);

        // uk
        $this->addFieldset('UK/ Non UK');
        $this->addCheckbox('changeUK', 'Change:', 1)->addAtrib('class', 'isChange');
        $this->addRadio('type', 'Type *', array(1 => 'UK', 2 => 'Non UK'))
            ->addRule(array(CHValidator::class, 'Validator_directorEditChangeRequired'), 'Please provide UK type!', 'changeUK');
        $this->addSelect('place_registered', 'Country Registered *', array_merge(['' => 'Choose an option'],$this->chCountriesRepository->getPersonCountriesOfResidence()))
            ->class('form-control')
            ->addRule(array(CHValidator::class, 'Validator_directorEditUKRequired'), 'Please provide Country registered!');
        $this->addText('registration_number', 'Registration number *')
            ->class('form-control')
            ->addRule(array(CHValidator::class, 'Validator_directorEditUKRequired'), 'Please provide Registration number!');
        $this->addText('law_governed', 'Governing law *')
            ->class('form-control')
            ->addRule(array(CHValidator::class, 'Validator_directorEditUKRequired'), 'Please provide Governing law!');
        $this->addText('legal_form', 'Legal From *')
            ->class('form-control')
            ->addRule(array(CHValidator::class, 'Validator_directorEditUKRequired'), 'Please provide Legal form!');

        if ($this->company->getType() == 'LLP') {

            $this->addFieldset('Designation');
            $this->addCheckbox('changeDesignation', 'Change:', 1)->addAtrib('class', 'isChange');

            $this->addRadio('designated_ind', 'Designated member *', array(0 => 'No', 1 => 'Yes'))
                ->addRule(array(CHValidator::class, 'Validator_directorLLPEditChangeRequired'), 'Please provide DesignatedInd', 'changeDesignation');

            $this->add(SingleCheckbox::class, 'consentToAct', 'The Limited Liability Partnership (LLP) confirms that the corporate body named has consented to act as a designated member')
                ->addRule(array(CHValidator::class, 'Validator_directorEditChangeRequired'), 'Consent to act is required', 'changeDesignation')
                ->{'data-label-designated_ind-0'}('The Limited Liability Partnership (LLP) confirms that the corporate body named has consented to act as a non-designated member')
                ->{'data-label-designated_ind-1'}('The Limited Liability Partnership (LLP) confirms that the corporate body named has consented to act as a designated member');
        }

        // submit				
        $this->addFieldset('Action');
        $this->addSubmit('continue', 'Save')->class('btn btn-primary btn-lg');

        //geting and seting form fields
        $fields = $this->director->getFields();
        unset($fields['consentToAct']);
        if ($this->company->getType() == 'LLP') {
            $fields['designated_ind'] = $this->director->getDesignatedInd();
        }

        $this->setInitValues($fields);

        $this->onValid = $this->callback;
        $this->start();
    }

    public function process()
    {
        $data = $this->getValues();

        // change name
        $change = array();
        if ($data['changeName'] == 1) {
            $change[] = 'changeName';
        }
        if ($data['changeAddress'] == 1) {
            $change[] = 'changeAddress';
        }
        if ($data['changeUK'] == 1) {
            $change[] = 'changeUK';
        }

        if ($this->company->getType() == 'LLP') {
            $change[] = 'changeDesignation';
        }
        
        $this->company->sendChangeOfCorporateDirector($this->director, $data, $change);
        $this->clean();
    }

}
