<?php

namespace Models\Products;

use BasketModule\Contracts\IProduct as IBasketProduct;
use Entities\Company;
use Entities\Customer as CustomerEntity;
use Entities\OrderItem;
use Entities\Payment\Token;
use Entities\Service;
use Exceptions\Business\EquivalentOrIncludedProductAlreadyInBasketException;
use Framework\FApplication;
use Framework\FImage;
use Framework\FNode;
use Libs\Basket;
use Masterminds\HTML5\Entities;
use Models\OldModels\Customer;
use Models\OldModels\OrderItem as OldOrderItem;
use Nette\Utils\Arrays;
use ProductModule\Providers\ExperimentProductPriceProvider;
use Services\Registry;
use Utils\Helpers\ArrayHelper;

class BasketProduct extends FNode implements IProduct, IBasketProduct
{
    public const LINK_EXISTING_SELECT = 'LINK_EXISTING_SELECT';
    public const LINK_EXISTING_IMPORT = 'LINK_EXISTING_IMPORT';
    public const LINK_NEW_SELECT = 'LINK_NEW_SELECT';
    public const LINK_NEW_IMPORT = 'LINK_NEW_IMPORT';

    public const AVAILABLE_FOR_PAY_BY_PHONE_NOT_AVAILABLE = 'NOT_AVAILABLE';
    public const AVAILABLE_FOR_PAY_BY_PHONE_COMPANY_FORMATION = 'COMPANY_FORMATION';
    public const AVAILABLE_FOR_PAY_BY_PHONE_RENEWALS = 'RENEWALS';
    public const AVAILABLE_FOR_PAY_BY_PHONE_OTHER_PRODUCTS = 'OTHER_PRODUCTS';
    public const AVAILABLE_FOR_PAY_BY_PHONE_ASSOCIATED_PRODUCTS = 'ASSOCIATED_PRODUCTS';

    public const PROPERTY_AVAILABLE_FOR_PAY_BY_PHONE = 'availableForPayByPhone';

    public const CUSTOMER_TYPE_WHOLESALE_ONLY = 'WHOLESALE_ONLY';
    public const CUSTOMER_TYPE_RETAIL_ONLY = 'RETAIL_ONLY';
    public const CUSTOMER_TYPE_ALL = 'ALL';
    public const CUSTOMER_TYPE_NONE = 'NONE';

    public const CUSTOMER_TYPES = [
        self::CUSTOMER_TYPE_ALL,
        self::CUSTOMER_TYPE_RETAIL_ONLY,
        self::CUSTOMER_TYPE_WHOLESALE_ONLY,
        self::CUSTOMER_TYPE_NONE,
    ];

    public const CUSTOMER_TYPE_NAMES = [
        self::CUSTOMER_TYPE_ALL => 'All',
        self::CUSTOMER_TYPE_RETAIL_ONLY => 'Retail Only',
        self::CUSTOMER_TYPE_WHOLESALE_ONLY => 'Wholesale Only',
        self::CUSTOMER_TYPE_NONE => 'None',
    ];

    /** @var array */
    public static $voServiceDurationsInMonths = [
        3 => '3 months',
        12 => '12 months',
    ];

    /**
     * @var array
     */
    public static $subscriptionTexts = [
        '+12 months' => 'Yearly subscription',
    ];

    /** @var int */
    public $qty = 1;

    /** @var float */
    public $productValue = 0.00;

    /** @var float */
    public $associatedPrice = 0.00;

    /** @var float */
    public $wholesalePrice = 0.00;

    /** @var bool */
    public $onlyOneItem = false;

    /** @var bool */
    public $maxQuantityOne = false;

    /** @var string */
    public $companyName;

    /** @var string */
    public $companyNumber;

    /** @var int */
    public $conditionedById;

    /** @var object Product */
    public $conditionedBy;

    /** @var bool */
    public $requiredCompanyNumber = false;

    /** @var bool */
    public $requiredIncorporatedCompanyNumber = false;

    /** @var bool */
    public $onlyOurCompanies = false;

    /** @var bool */
    public $saveToCompany = false;

    /** @var string */
    public $responsibleEmails;

    /** @var bool */
    public $notApplyVat = false;

    /** @var int */
    public $nonVatableValue = 0;

    /** @var string */
    public $nonVatableDescription;

    /** @var int */
    public $emailText;

    /** @var int */
    public $basketText;

    /** @var array */
    public $emailCmsFileAttachments = [];

    /** @var bool */
    public $isAssociated = false;

    /** @var string */
    public $additional;

    /** @var bool */
    public $lockCompany = false;

    /** @var string */
    public $upgradeDescription;

    /** @var int */
    public $upgradeImageId;

    /** @var int */
    public $associatedImageId;

    /** @var string */
    public $associatedText;

    /**
     * @var string
     */
    public $associatedDescription;

    /**
     * @var string
     */
    public $associatedIconClass;

    /** @var float */
    public $markUp = 0.00;

    /** @var bool */
    public $isFeefoEnabled = false;

    /**
     * @var bool
     */
    public $isAutoRenewalAllowed = false;

    /**
     * @var Token
     */
    public $autoRenewalToken;

    /**
     * @var string
     */
    public $linkedCompanyStatus;

    /**
     * @var OldOrderItem
     */
    public $oldOrderItem;

    /**
     * @var OrderItem
     */
    public $orderItem;

    /**
     * @var string
     */
    public $serviceTypeId;

    /**
     * @var int
     */
    public $renewalProductId;

    /**
     * @var int
     */
    public $optionalRenewalProductId;

    /**
     * @var string
     */
    public $renewalDtStartBehavior;

    /**
     * @var BasketProduct[]
     */
    public $blacklistedEquivalentProducts = [];

    /**
     * @var BasketProduct[]
     */
    public $blacklistedContainedProducts = [];

    /**
     * @var int[]
     *            Upsell products list
     */
    public $associatedProducts3 = [];

    /**
     * @var int
     */
    public $offerProductId;

    /**
     * @var bool
     */
    public $isIdCheckRequired;

    /**
     * @var bool
     */
    public $bankingEnabled = false;

    /**
     * @var bool
     */
    public $bankingRequired = false;

    /**
     * @var array
     */
    public $bankingOptions = [];

    /**
     * @var bool
     */
    public $printedCertificateOptionEnabled = false;

    /**
     * @var string
     */
    public $sageNominalCode;

    /**
     * @var bool
     */
    public $registrationReviewEnabled = false;

    /**
     * @var float
     */
    public $renewalEmailPrice = 0.00;

    /**
     * @var float
     */
    public $specialPrice = 0.00;

    /**
     * @var array
     */
    public $additionalInformation = [];

    /**
     * @var bool
     */
    public $requiredCorePackage;

    /**
     * @var bool
     */
    public $customAssociatedIcon;

    /**
     * @var
     */
    public $reference;

    /**
     * @var int
     */
    public $quotas;

    /**
     * @var int
     */
    public $mailboxAcceptanceStatutory;

    /**
     * @var int
     */
    public $mailboxAcceptanceNonStatutory;

    /**
     * @var int
     */
    public $mailboxAcceptanceParcel;

    /**
     * @var int
     */
    public $mailboxQuotaStatutoryScanned;

    /**
     * @var int
     */
    public $mailboxQuotaStatutoryCollect;

    /**
     * @var int
     */
    public $mailboxQuotaStatutoryPost;

    /**
     * @var int
     */
    public $mailboxQuotaNonStatutoryScanned;

    /**
     * @var int
     */
    public $mailboxQuotaNonStatutoryCollect;

    /**
     * @var int
     */
    public $mailboxQuotaNonStatutoryPost;

    /**
     * @var int
     */
    public $mailboxQuotaParcelCollect;

    /**
     * @var int
     */
    public $mailboxQuotaParcelPost;

    /**
     * @var float
     */
    public $mailboxExtraQuotaPostItemFee;

    /**
     * @var float
     */
    public $mailboxExtraQuotaParcelFee;

    /**
     * @var float
     */
    public $mailboxHandlingFeePostItem;

    /**
     * @var float
     */
    public $mailboxHandlingFeeParcel;

    /**
     * @var float
     */
    public $mailboxForwardingFeePostItem;

    /**
     * @var float
     */
    public $mailboxForwardingFeeParcel;

    /**
     * @var float
     */
    public $mailboxPayToReleaseFeePostItem;

    /**
     * @var float
     */
    public $mailboxPayToReleaseFeeParcel;

    /**
     * @var int
     */
    public $mailboxSettingPostItem;

    /**
     * @var int
     */
    public $mailboxSettingParcel;

    /**
     * @var string
     */
    public $customerType = self::CUSTOMER_TYPE_ALL;

    /**
     * @var Company
     */
    protected $companyEntity;

    /**
     * @var bool
     */
    protected $isVoServiceEligible = false;

    /**
     * @var int
     */
    protected $voServiceDurationInMonths;

    /** @var float */
    private $price = 0.00;

    /** @var int */
    private $saving = 0.00;

    /**
     * @var int
     */
    private $companyId;

    /**
     * @var string
     */
    private $duration;

    /**
     * @var \DateTimeInterface
     */
    private $activateFrom;

    /**
     * @var bool
     */
    private $removableFromBasket = true;

    /**
     * @var string
     */
    private $removeFromBasketConfirmation;

    /**
     * @var float
     */
    private $offerPrice = 0.00;

    /**
     * @var bool
     */
    private $isOffer = false;

    private $availableForPayByPhone = self::AVAILABLE_FOR_PAY_BY_PHONE_NOT_AVAILABLE;

    /**
     * @var array
     */
    private $toolkitOfferTypes = [];

    /**
     * @var bool
     */
    private $isInitialProduct = false;

    /**
     * @var bool
     */
    private $isRenewalProduct = false;

    /**
     * @var bool
     */
    private $showInMyServicesPage = false;

    /**
     * @var IBasketProduct
     */
    private $child;

    private bool $requiresUkDirector = false;
    private bool $requiresAdultDirector = false;
    private bool $isBankingPartnerPackage = false;
    private bool $isBusinessPartnerPackage = false;

    public function saveCustomData($action)
    {
        $this->saveProperty('price', $this->price);
        $this->saveProperty('nonVatableValue', $this->nonVatableValue);
        $this->saveProperty('nonVatableDescription', $this->nonVatableDescription);
        $this->saveProperty('associatedPrice', $this->associatedPrice);
        $this->saveProperty('wholesalePrice', $this->wholesalePrice);
        $this->saveProperty('offerPrice', $this->offerPrice);
        $this->saveProperty('productValue', $this->productValue);
        $this->saveProperty('requiredCompanyNumber', $this->requiredCompanyNumber);
        $this->saveProperty('requiredIncorporatedCompanyNumber', $this->requiredIncorporatedCompanyNumber);
        $this->saveProperty('onlyOurCompanies', $this->onlyOurCompanies);
        $this->saveProperty('saveToCompany', $this->saveToCompany);
        $this->saveProperty('onlyOneItem', $this->onlyOneItem);
        $this->saveProperty('maxQuantityOne', $this->maxQuantityOne);
        $this->saveProperty('conditionedById', $this->conditionedById);
        $this->saveProperty('responsibleEmails', $this->responsibleEmails);
        $this->saveProperty('notApplyVat', $this->notApplyVat);
        $this->saveProperty('emailText', $this->emailText);
        $this->saveProperty('basketText', $this->basketText);
        $this->saveProperty('additional', $this->additional);
        $this->saveProperty('upgradeDescription', $this->upgradeDescription);
        $this->saveProperty('upgradeImageId', $this->upgradeImageId);
        $this->saveProperty('markUp', $this->markUp);
        $this->saveProperty('associatedImageId', $this->associatedImageId);
        $this->saveProperty('associatedText', $this->associatedText);
        $this->saveProperty('associatedDescription', $this->associatedDescription);
        $this->saveProperty('associatedIconClass', $this->associatedIconClass);
        $this->saveProperty('isFeefoEnabled', $this->isFeefoEnabled);
        $this->saveProperty('isAutoRenewalAllowed', $this->isAutoRenewalAllowed);
        $this->saveProperty(BasketProduct::PROPERTY_AVAILABLE_FOR_PAY_BY_PHONE, $this->availableForPayByPhone);
        $this->saveProperty('toolkitOfferTypes', implode(',', $this->toolkitOfferTypes));
        $this->saveProperty('requiresUkDirector', $this->requiresUkDirector);
        $this->saveProperty('requiresAdultDirector', $this->requiresAdultDirector);
        $this->saveProperty('isBankingPartnerPackage', $this->isBankingPartnerPackage);
        $this->saveProperty('isBusinessPartnerPackage', $this->isBusinessPartnerPackage);

        // email attachments
        // add
        if (!empty($this->emailCmsFileAttachments)) {
            $emailcmsFileAttachments = implode(',', $this->emailCmsFileAttachments);
            $this->saveProperty('emailCmsFileAttachments', $emailcmsFileAttachments);
        // remove
        } else {
            $this->saveProperty('emailCmsFileAttachments', '');
        }

        // save blacklisted products
        $arr = ['blacklistedEquivalentProducts', 'blacklistedContainedProducts'];
        foreach ($arr as $key => $val) {
            $products = null;
            if (!empty($this->$val)) {
                $products = implode(',', $this->$val);
            }
            $this->saveProperty($val, $products);
        }

        $upsellProducts = $this->associatedProducts3 ? implode(',', $this->associatedProducts3) : '';
        $this->saveProperty('associatedProducts3', $upsellProducts);

        $offerProductId = $this->offerProductId ?: '';
        $this->saveProperty('offerProductId', $offerProductId);

        $this->saveProperty('lockCompany', $this->lockCompany);
        $this->saveProperty('serviceTypeId', $this->serviceTypeId);
        $this->saveProperty('duration', $this->getDuration());
        $this->saveProperty('renewalProductId', $this->renewalProductId);
        $this->saveProperty('optionalRenewalProductId', $this->optionalRenewalProductId);
        $this->saveProperty('renewalDtStartBehavior', $this->renewalDtStartBehavior);

        // vo eligible products
        $this->saveProperty('isVoServiceEligible', (bool) $this->isVoServiceEligible);
        $this->saveProperty('voServiceDurationInMonths', (int) $this->voServiceDurationInMonths);
        $this->saveProperty('isIdCheckRequired', $this->isIdCheckRequired);

        $this->saveProperty('bankingEnabled', $this->bankingEnabled);
        $this->saveProperty('bankingOptions', implode(',', $this->bankingOptions));
        $this->saveProperty('bankingRequired', $this->bankingRequired);

        $this->saveProperty('removableFromBasket', $this->removableFromBasket);
        $this->saveProperty('removeFromBasketConfirmation', $this->removeFromBasketConfirmation);

        $this->saveProperty('isInitialProduct', (bool) $this->isInitialProduct());
        $this->saveProperty('isRenewalProduct', (bool) $this->isRenewalProduct());
        $this->saveProperty('showInMyServicesPage', (bool) $this->showInMyServicesPage);

        // incorporation certificate
        $this->saveProperty('printedCertificateOptionEnabled', $this->printedCertificateOptionEnabled);
        $this->saveProperty('registrationReviewEnabled', $this->registrationReviewEnabled);
        $this->saveProperty('sageNominalCode', $this->sageNominalCode);

        if ($this->isRenewalProduct()) {
            $this->saveProperty('renewalEmailPrice', $this->renewalEmailPrice);
        }

        $this->saveProperty('specialPrice', $this->specialPrice);

        $this->saveProperty('requiredCorePackage', $this->requiredCorePackage);
        $this->saveProperty('customAssociatedIcon', $this->customAssociatedIcon);

        if ($this->quotas && is_numeric($this->quotas)) {
            $this->saveProperty('quotas', $this->quotas);
        }

        $this->saveProperty('mailbox-acceptance-statutory', $this->mailboxAcceptanceStatutory);
        $this->saveProperty('mailbox-acceptance-non-statutory', $this->mailboxAcceptanceNonStatutory);
        $this->saveProperty('mailbox-acceptance-parcel', $this->mailboxAcceptanceParcel);
        $this->saveProperty('mailbox-quota-statutory-scanned', $this->mailboxQuotaStatutoryScanned);
        $this->saveProperty('mailbox-quota-statutory-collect', $this->mailboxQuotaStatutoryCollect);
        $this->saveProperty('mailbox-quota-statutory-post', $this->mailboxQuotaStatutoryPost);
        $this->saveProperty('mailbox-quota-non-statutory-scanned', $this->mailboxQuotaNonStatutoryScanned);
        $this->saveProperty('mailbox-quota-non-statutory-collect', $this->mailboxQuotaNonStatutoryCollect);
        $this->saveProperty('mailbox-quota-non-statutory-post', $this->mailboxQuotaNonStatutoryPost);
        $this->saveProperty('mailbox-quota-parcel-collect', $this->mailboxQuotaParcelCollect);
        $this->saveProperty('mailbox-quota-parcel-post', $this->mailboxQuotaParcelPost);
        $this->saveProperty('mailbox-extra-quota-post-item-fee', $this->mailboxExtraQuotaPostItemFee);
        $this->saveProperty('mailbox-extra-quota-parcel-fee', $this->mailboxExtraQuotaParcelFee);
        $this->saveProperty('mailbox-handling-fee-post-item', $this->mailboxHandlingFeePostItem);
        $this->saveProperty('mailbox-handling-fee-parcel', $this->mailboxHandlingFeeParcel);
        $this->saveProperty('mailbox-forwarding-fee-post-item', $this->mailboxForwardingFeePostItem);
        $this->saveProperty('mailbox-forwarding-fee-parcel', $this->mailboxForwardingFeeParcel);
        $this->saveProperty('mailbox-setting-post-item', $this->mailboxSettingPostItem);
        $this->saveProperty('mailbox-setting-parcel', $this->mailboxSettingParcel);
        $this->saveProperty('mailbox-pay-to-release-fee-post-item', $this->mailboxPayToReleaseFeePostItem);
        $this->saveProperty('mailbox-pay-to-release-fee-parcel', $this->mailboxPayToReleaseFeeParcel);

        $this->saveProperty('customerType', $this->customerType);
    }

    public function setPrice($price)
    {
        $this->price = $price;
    }

    public function setAllPrices(float $price): void
    {
        $this->price = $price;
        $this->wholesalePrice = $price;
        $this->associatedPrice = $price;
        $this->offerPrice = $price;
        $this->specialPrice = $price;
        $this->productValue = $price;
    }

    public function setSaving($saving)
    {
        $this->saving = $saving;
    }

    public function getStringPrice(): string
    {
        return (string) $this->price;
    }

    public function getStringWholesalePrice(): string
    {
        return (string) $this->wholesalePrice;
    }

    /**
     * @param CustomerEntity|null $customer
     *
     * @return float
     */
    public function getPrice(?CustomerEntity $customer = null): ?float
    {
        /** @var ExperimentProductPriceProvider $experimentProductPriceProvider */
        $experimentProductPriceProvider = Registry::getService('product_module.providers.experiment_product_price_provider');
        if ($price = $experimentProductPriceProvider->getExperimentPrice($this->getNodeName())) {
            return $price;
        }

        if ($this->isOffer() && $this->offerPrice !== null) {
            return $this->offerPrice;
        }
        if ($this->isAssociated === true) {
            return $this->associatedPrice;
        }
        if (Customer::isSignedIn() && Customer::getSignedIn()->isWholesale() && $this->wholesalePrice) {
            return $this->wholesalePrice;
        }
        if ($customer && $customer->isWholesale() && $this->wholesalePrice) {
            return $this->wholesalePrice;
        }

        return $this->price;
    }

    public function getSaving()
    {
        return $this->productValue - $this->getPrice();
    }

    /**
     * @param CustomerEntity|null $customer
     *
     * @return float
     */
    public function getTotalPrice(?CustomerEntity $customer = null)
    {
        $price = $this->getPrice($customer) * $this->qty;

        return Basket::round($price);
    }

    public function getOriginalPrice()
    {
        return $this->price;
    }

    /**
     * Returns longer title for product.
     *
     * @return string
     */
    public function getLongTitle()
    {
        $longTitle = $this->getLngTitle();
        if ($this->companyName !== null) {
            $longTitle .= ' for company "' . $this->companyName . '"';
        } elseif ($this->companyNumber !== null) {
            $longTitle .= ' for company "' . $this->companyNumber . '"';
        }

        return $longTitle;
    }

    /**
     * @param object Basket $basket
     *
     * @return bool
     */
    public function canBeAdded(Basket $basket)
    {
        if ($this->isOk2show() === false || $this->isBlacklistedInBasket($basket)
            || ($this->inBasket($basket) !== false && $this->onlyOneItem)) {
            return false;
        }
        if (!empty($this->conditionedById)) {
            $conditionedItem = self::getProductById($this->conditionedById);
            if ($conditionedItem->inBasket($basket) === false) {
                return false;
            }
        }

        if ($this->getNodeName() === Product::PRODUCT_CSMS_REPORT
            && empty($this->getAdditionalInformationProperty('csmsCompanyNumber'))
        ) {
            return false;
        }

        if (!$basket->hasPackageIncluded() && $this->isRequiredCorePackage()) {
            return false;
        }

        return $this->isLinkedOrNoPackage($basket);
    }

    public function isLinkedOrNoPackage(Basket $basket): bool
    {
        return !($basket->hasPackageIncluded() && $this->isLinkedToExistingCompany());
    }

    /**
     * @param Basket $basket
     *
     * @throws \Exception
     *
     * @return bool
     */
    public function isBlackListedInBasket(Basket $basket)
    {
        foreach ($basket->getItems() as $item) {
            if (in_array($this->getId(), $item->blacklistedEquivalentProducts) && $this->matchesCompany($item)) {
                $message = sprintf(
                    'Sorry, you are trying to purchase the %s but you already have the %s in your basket. At this time we only support the purchase of one company at a time.',
                    $this->getLngTitle(),
                    $item->getLngTitle()
                );
                throw new EquivalentOrIncludedProductAlreadyInBasketException($message);
            }
            if (in_array($this->getId(), $item->blacklistedContainedProducts) && $this->matchesCompany($item)) {
                $message = sprintf(
                    'The "%s" is already included in your "%s", so we have removed the extra "%s" from your basket.',
                    $this->getLngTitle(),
                    $item->getLngTitle(),
                    $this->getLngTitle()
                );
                throw new EquivalentOrIncludedProductAlreadyInBasketException($message);
            }
        }

        return false;
    }

    /**
     * @param Basket $basket
     *
     * @return bool
     */
    public function shouldReplaceContainedBlacklistedProducts(Basket $basket)
    {
        foreach ($basket->getItems() as $item) {
            if (in_array($item->getId(), $this->blacklistedContainedProducts)) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param object Basket $basket
     *
     * @return bool
     */
    public function canBeUpdated(Basket $basket)
    {
        if (($this->inBasket($basket) !== false && $this->onlyOneItem) || $this->maxQuantityOne) {
            return false;
        }

        return true;
    }

    /**
     * @param object Basket $basket
     *
     * @return bool
     */
    public function inBasket(Basket $basket)
    {
        foreach ($basket->getItems() as $key => $item) {
            if ($item->getClass() == $this->getClass() && $this->getId() == $item->getId()) {
                return $key;
            }
        }

        return false;
    }

    /**
     * @param object Basket $basket
     *
     * @return bool
     */
    public function inBasketForCompany(Basket $basket): bool
    {
        foreach ($basket->getItems() as $key => $item) {
            if ($this->isEqual($item) && $this->getCompanyId() == $item->getCompanyId()) {
                return $key;
            }
        }

        return false;
    }

    /**
     * @param BasketProduct $basketProduct
     *
     * @return bool
     */
    public function isEqual(BasketProduct $basketProduct): bool
    {
        return $basketProduct->getClass() == $this->getClass()
        && $this->getId() == $basketProduct->getId()
            ? true
            : false;
    }

    /**
     * @param int $qty
     *
     * @return void
     */
    public function setQty($qty)
    {
        $this->qty = max(1, $qty);
    }

    /**
     * @param CustomerEntity $customer
     *
     * @return float
     */
    public function getVat(?CustomerEntity $customer = null)
    {
        // no VAT
        if ($this->notApplyVat == true) {
            $vat = 0.00;
        // non vatable value
        } elseif ($this->nonVatableValue) {
            $price = $this->getTotalPrice($customer) - $this->nonVatableValue;
            $vat = $price * Basket::VAT;
        } else {
            $vat = $this->getTotalPrice($customer) * Basket::VAT;
        }

        return Basket::round($vat);
    }

    /**
     * @param CustomerEntity|null $customer
     *
     * @return float
     */
    public function getTotalVatPrice(?CustomerEntity $customer = null)
    {
        $price = $this->getTotalPrice($customer) + $this->getVat($customer);

        return Basket::round($price);
    }

    /**
     * Returns all products.
     *
     * @param bool $returnAsObjects
     *
     * @return array
     */
    public static function getAllProducts($returnAsObjects = false)
    {
        $products = [];
        $blackList = ['FolderAdminControler', /* 'PackageAdminControler', */
            'JourneyProductAdminControler'];
        foreach (FNode::getChildsNodes(107) as $key => $val) {
            if (!in_array($val->adminControler, $blackList)) {
                if ($val->getId() === Product::PRODUCT_FRAUD_PROTECTION) {
                    $products[$key] = $val->getTitle();
                    continue;
                }
                $products[$key] = $val->getLngTitle();
            }
        }
        asort($products);

        return $products;
    }

    /**
     * Returns all packages.
     *
     * @param bool $returnAsObjects
     *
     * @return array
     */
    public static function getAllPackages($returnAsObjects = false)
    {
        $packages = [];
        $whiteList = ['PackageAdminControler', 'WholesalePackageAdminControler'];
        foreach (FNode::getChildsNodes(107) as $key => $val) {
            if (in_array($val->adminControler, $whiteList)) {
                $packages[$key] = $val->getLngTitle();
            }
        }

        return $packages;
    }

    /**
     * @return FImage
     */
    public function getUpgradeImageImage()
    {
        try {
            $image = new FImage($this->upgradeImageId);
        } catch (\Exception $e) {
            $image = new FImage();
        }

        return $image;
    }

    public function getAssociatedProductImage()
    {
        try {
            $image = new FImage($this->associatedImageId);
        } catch (\Exception $e) {
            $image = null;
        }

        return $image;
    }

    /**
     * @return bool
     */
    public function isFeefo()
    {
        return (bool) $this->isFeefoEnabled;
    }

    /**
     * @return bool
     */
    public function hasServiceType()
    {
        return (bool) $this->serviceTypeId;
    }

    /**
     * @return bool
     */
    public function hasRenewalProduct()
    {
        return (bool) $this->renewalProductId;
    }

    /**
     * @return Product
     */
    public function getRenewalProduct()
    {
        return FNode::getProductById($this->renewalProductId);
    }

    public function isOptionalProduct($productId)
    {
        return $this->optionalRenewalProductId === $productId;
    }

    public function hasOptionalRenewalProduct()
    {
        return (bool) $this->optionalRenewalProductId;
    }

    public function getOptionalRenewalProduct()
    {
        return FNode::getProductById($this->optionalRenewalProductId);
    }

    /**
     * @return Entities|Company
     */
    public function getCompanyEntity()
    {
        return $this->companyEntity;
    }

    /**
     * @param Entities|Company $companyEntity
     */
    public function setCompanyEntity(Entities|Company $companyEntity)
    {
        $this->companyEntity = $companyEntity;
    }

    public function fillCompanyNameNumberFromEntity(): void
    {
        if ($company = $this->getCompanyEntity()) {
            $this->companyName = $company->getCompanyName();
            if ($company->isIncorporated()) {
                $this->companyNumber = $company->getCompanyNumber();
            }
        }
    }

    /**
     * @return bool
     */
    public function isLinkedToExistingCompany()
    {
        return $this->linkedCompanyStatus == self::LINK_EXISTING_SELECT || $this->linkedCompanyStatus == self::LINK_EXISTING_IMPORT;
    }

    /**
     * @return bool
     */
    public function isLinkedToExistingImportedCompany()
    {
        return $this->linkedCompanyStatus == self::LINK_EXISTING_IMPORT;
    }

    /**
     * @return bool
     */
    public function isLinkedToNewImportedCompany()
    {
        return $this->linkedCompanyStatus == self::LINK_NEW_IMPORT;
    }

    /**
     * @return bool
     */
    public function isLinkedToNewCompany()
    {
        return $this->linkedCompanyStatus == self::LINK_NEW_IMPORT || $this->linkedCompanyStatus == self::LINK_NEW_SELECT;
    }

    /**
     * @return bool
     */
    public function isLinkedToImportedCompany()
    {
        return $this->linkedCompanyStatus == self::LINK_NEW_IMPORT || $this->linkedCompanyStatus == self::LINK_EXISTING_IMPORT;
    }

    /**
     * @return string
     */
    public function getSubscriptionPeriodText()
    {
        return Arrays::get(self::$subscriptionTexts, $this->getDuration(), '');
    }

    /**
     * @param bool $isVoServiceEligible
     */
    public function setVoServiceEligible($isVoServiceEligible)
    {
        $this->isVoServiceEligible = (bool) $isVoServiceEligible;
    }

    /**
     * @param int $voServiceDurationInMonths
     */
    public function setVoServiceDurationInMonths($voServiceDurationInMonths)
    {
        $this->voServiceDurationInMonths = (int) $voServiceDurationInMonths;
    }

    /**
     * @return bool
     */
    public function isVoServiceEligible()
    {
        return $this->isVoServiceEligible;
    }

    /**
     * @return int
     */
    public function getVoServiceDurationInMonths()
    {
        return $this->voServiceDurationInMonths;
    }

    /**
     * @return bool
     */
    public function isFree()
    {
        return $this->price == 0;
    }

    /**
     * @return bool
     */
    public function isPackage()
    {
        return $this instanceof IPackage;
    }

    /**
     * @return bool
     */
    public function isProduct()
    {
        return !$this->isPackage();
    }

    /**
     * @return int
     */
    public function getCompanyId()
    {
        return $this->companyId;
    }

    /**
     * @param int $companyId
     */
    public function setCompanyId($companyId)
    {
        $this->companyId = $companyId;
    }

    /**
     * @return string
     */
    public function getCompanyNumber()
    {
        return $this->companyNumber;
    }

    /**
     * @return string
     */
    public function getServiceTypeId()
    {
        return $this->serviceTypeId;
    }

    /**
     * @return OrderItem
     */
    public function getOrderItem()
    {
        return $this->orderItem;
    }

    /**
     * @param OrderItem $orderItem
     */
    public function setOrderItem(OrderItem $orderItem)
    {
        $this->orderItem = $orderItem;
    }

    /**
     * @param $removable
     */
    public function setRemovableFromBasket($removable)
    {
        $this->removableFromBasket = $removable;
    }

    /**
     * @return bool
     */
    public function isRemovableFromBasket()
    {
        return $this->removableFromBasket;
    }

    /**
     * @return string
     */
    public function getRemoveFromBasketConfirmation()
    {
        return $this->removeFromBasketConfirmation;
    }

    /**
     * @param string $confirmation
     */
    public function setRemoveFromBasketConfirmation($confirmation)
    {
        $this->removeFromBasketConfirmation = $confirmation;
    }

    /**
     * @return bool
     */
    public function hasRemoveFromBasketConfirmation()
    {
        return (bool) $this->removeFromBasketConfirmation;
    }

    /**
     * @return string
     */
    public function getDuration()
    {
        return $this->duration;
    }

    /**
     * @param string $duration
     */
    public function setDuration($duration)
    {
        $this->duration = $duration;
    }

    /**
     * @param \DateTimeInterface $activateFrom
     */
    public function setActivateFrom(\DateTimeInterface $activateFrom)
    {
        $this->activateFrom = $activateFrom;
    }

    /**
     * @return \DateTimeInterface
     */
    public function getActivateFrom()
    {
        return $this->activateFrom;
    }

    /**
     * @return bool
     */
    public function isPackagePrivacyType()
    {
        return $this->serviceTypeId == Service::TYPE_PACKAGE_PRIVACY;
    }

    /**
     * @return bool
     */
    public function isPackageComprehensiveType()
    {
        return $this->serviceTypeId == Service::TYPE_PACKAGE_COMPREHENSIVE;
    }

    /**
     * @return bool
     */
    public function isPackageUltimateType()
    {
        return $this->serviceTypeId == Service::TYPE_PACKAGE_ULTIMATE;
    }

    /**
     * @return bool
     */
    public function isPackageComprehensiveUltimateType()
    {
        return $this->serviceTypeId == Service::TYPE_PACKAGE_COMPREHENSIVE_ULTIMATE;
    }

    public function isMailboxStandardProduct(): bool
    {
        return $this->serviceTypeId == Service::TYPE_MAILBOX_STANDARD;
    }

    public function isMailboxPremiumProduct(): bool
    {
        return $this->serviceTypeId == Service::TYPE_MAILBOX_PREMIUM;
    }

    public function isMailboxBusinessProduct(): bool
    {
        return $this->getNodeName() === Product::PRODUCT_MAILBOX_BUSINESS_ADDRESS_RENEWAL
            || $this->getNodeName() === Product::PRODUCT_MAILBOX_BUSINESS_ADDRESS_INITIAL
            || $this->getNodeName() === Product::PRODUCT_MAILBOX_BUSINESS_ADDRESS_INITIAL_UPSELL;
    }

    /**
     * @return bool
     */
    public function isServiceWithAutoRenewalAllowed()
    {
        return $this->serviceTypeId && $this->isAutoRenewalAllowed;
    }

    /**
     * @return float
     */
    public function getProductValue()
    {
        return $this->productValue;
    }

    /**
     * @return float
     */
    public function getOfferPrice()
    {
        return empty($this->offerPrice) ? $this->price : $this->offerPrice;
    }

    /**
     * @param float $offerPrice
     */
    public function setOfferPrice($offerPrice)
    {
        $this->offerPrice = $offerPrice;
    }

    /**
     * @return bool
     */
    public function isOffer()
    {
        return $this->isOffer;
    }

    public function markAsOffer()
    {
        $this->isOffer = true;
    }

    public function unmarkAsOffer()
    {
        $this->isOffer = false;
    }

    /**
     * @return string
     */
    public function getAvailableForPayByPhone()
    {
        return $this->availableForPayByPhone;
    }

    /**
     * @param string $availableForPayByPhone
     */
    public function setAvailableForPayByPhone($availableForPayByPhone)
    {
        $this->availableForPayByPhone = $availableForPayByPhone;
    }

    /**
     * @return int
     */
    public function getQty()
    {
        return $this->qty;
    }

    /**
     * @return string
     */
    public function getName()
    {
        return $this->getLngTitle();
    }

    /**
     * @return int
     */
    public function getOfferProductId()
    {
        return $this->offerProductId;
    }

    /**
     * @param int $offerProductId
     */
    public function setOfferProductId($offerProductId)
    {
        $this->offerProductId = $offerProductId;
    }

    /**
     * @return bool
     */
    public function hasOfferProductId()
    {
        return !empty($this->offerProductId);
    }

    /**
     * Returns includes|associated products.
     *
     * @param string $type
     *
     * @return array
     */
    public function getPackageProducts($type)
    {
        $products = [];
        $controlers = FApplication::$db->select('node_id, admin_controler')->from(TBL_NODES)->where('node_id')
            ->in($this->$type)->execute()->fetchPairs();
        $namespace = 'AdminModule\controlers';
        foreach ($controlers as $nodeId => $controler) {
            $controlers[$nodeId] = sprintf('%s\%s', $namespace, $controler);
        }
        foreach ($this->$type as $key => $val) {
            if (isset($controlers[$val])) {
                $controler = $controlers[$val];
                $products[$val] = self::getProductByAdminControler($controler, $val);
            }
        }

        return $products;
    }

    /**
     * @param Basket $basket
     *
     * @return array
     */
    public function getNotInBasketAssociatedProducts3(Basket $basket)
    {
        $associatedProducts = $this->getPackageProducts('associatedProducts3');
        foreach ($associatedProducts as $key => &$product) {
            if ($product->inBasket($basket) !== false) {
                unset($associatedProducts[$key]);
                continue;
            }
            $product->isAssociated = true;
        }

        return $associatedProducts;
    }

    /**
     * @return bool
     */
    public function hasAssociatedUpsellProducts()
    {
        return !empty($this->associatedProducts3);
    }

    /**
     * @return int[]
     */
    public function getAssociatedProducts3()
    {
        return $this->associatedProducts3;
    }

    /**
     * @param int[] $associatedProducts3
     */
    public function setAssociatedProducts3($associatedProducts3)
    {
        $this->associatedProducts3 = $associatedProducts3;
    }

    public function hasAssociatedProducts()
    {
        return !empty($this->associatedProducts3);
    }

    public function getBankingOptions()
    {
        return $this->bankingOptions;
    }

    public function hasPrintedCertificateOptionEnabled(): bool
    {
        return $this->printedCertificateOptionEnabled;
    }

    /**
     * @param bool $printedCertificateOptionEnabled
     */
    public function setPrintedCertificateOptionEnabled(bool $printedCertificateOptionEnabled): void
    {
        $this->printedCertificateOptionEnabled = $printedCertificateOptionEnabled;
    }

    /**
     * @return array
     */
    public function getToolkitOfferTypes()
    {
        return $this->toolkitOfferTypes;
    }

    /**
     * @param array $toolkitOfferTypes
     */
    public function setToolkitOfferTypes(array $toolkitOfferTypes)
    {
        $this->toolkitOfferTypes = $toolkitOfferTypes;
    }

    /**
     * @return bool
     */
    public function isInitialProduct(): bool
    {
        return $this->isInitialProduct;
    }

    /**
     * @param bool $isInitialProduct
     */
    public function setIsInitialProduct(bool $isInitialProduct)
    {
        $this->isInitialProduct = $isInitialProduct;
    }

    /**
     * @return bool
     */
    public function isRenewalProduct(): bool
    {
        return $this->isRenewalProduct;
    }

    /**
     * @param bool $isRenewalProduct
     */
    public function setIsRenewalProduct(bool $isRenewalProduct)
    {
        $this->isRenewalProduct = $isRenewalProduct;
    }

    public function isRegistrationReviewEnabled(): bool
    {
        return $this->registrationReviewEnabled;
    }

    public function setRegistrationReviewEnabled(bool $isEnabled): void
    {
        $this->registrationReviewEnabled = $isEnabled;
    }

    public function setChild(BasketProduct $item)
    {
        $this->child = $item;
    }

    public function getChild(): ?IBasketProduct
    {
        return $this->child;
    }

    public function removeChild()
    {
        $this->child = null;
    }

    public function getRenewalEmailPrice(?CustomerEntity $customer = null, ?\DateTime $expiryDate = null): float
    {
        if ($this->isRenewalProduct() && isset($this->renewalEmailPrice) && $this->renewalEmailPrice > 0) {
            if ($this->getId() === Service::RENEWAL_PRODUCT_ID_PACKAGE_COMPREHENSIVE) {
                if ($expiryDate !== null && $expiryDate >= new \DateTime('2024-02-20 00:00:00')) {
                    return $this->renewalEmailPrice;
                }

                return $this->getPrice($customer);
            }

            return $this->renewalEmailPrice;
        }

        return $this->getPrice($customer);
    }

    public function setRenewalEmailPrice(float $renewalEmailPrice)
    {
        $this->renewalEmailPrice = $renewalEmailPrice;
    }

    public function hasSpecialPrice(): bool
    {
        return $this->getSpecialPrice() > 0;
    }

    public function getSpecialPrice(): float
    {
        /** @var ExperimentProductPriceProvider $experimentProductPriceProvider */
        $experimentProductPriceProvider = Registry::getService('product_module.providers.experiment_product_price_provider');
        $price = $experimentProductPriceProvider->getSpecialPrice($this->getNodeName());
        if ($price !== null) {
            return $price;
        }

        return $this->specialPrice;
    }

    public function hasQuotas(): bool
    {
        return !is_null($this->quotas) && $this->quotas > 0;
    }

    public function getQuotas(): int
    {
        return $this->hasQuotas() ? $this->quotas : 0;
    }

    public function setQuotas(int $quotas): void
    {
        $this->quotas = $quotas;
    }

    public function getMailboxAcceptanceStatutory(): int
    {
        return $this->mailboxAcceptanceStatutory;
    }

    public function setMailboxAcceptanceStatutory(int $mailboxAcceptanceStatutory): void
    {
        $this->mailboxAcceptanceStatutory = $mailboxAcceptanceStatutory;
    }

    public function getMailboxAcceptanceNonStatutory(): int
    {
        return $this->mailboxAcceptanceNonStatutory;
    }

    public function setMailboxAcceptanceNonStatutory(int $mailboxAcceptanceNonStatutory): void
    {
        $this->mailboxAcceptanceNonStatutory = $mailboxAcceptanceNonStatutory;
    }

    public function getMailboxAcceptanceParcel(): int
    {
        return $this->mailboxAcceptanceParcel;
    }

    public function setMailboxAcceptanceParcel(int $mailboxAcceptanceParcel): void
    {
        $this->mailboxAcceptanceParcel = $mailboxAcceptanceParcel;
    }

    public function getMailboxQuotaStatutoryScanned(): int
    {
        return $this->mailboxQuotaStatutoryScanned;
    }

    public function setMailboxQuotaStatutoryScanned(int $mailboxQuotaStatutoryScanned): void
    {
        $this->mailboxQuotaStatutoryScanned = $mailboxQuotaStatutoryScanned;
    }

    public function getMailboxQuotaStatutoryCollect(): int
    {
        return $this->mailboxQuotaStatutoryCollect;
    }

    public function setMailboxQuotaStatutoryCollect(int $mailboxQuotaStatutoryCollect): void
    {
        $this->mailboxQuotaStatutoryCollect = $mailboxQuotaStatutoryCollect;
    }

    public function getMailboxQuotaStatutoryPost(): int
    {
        return $this->mailboxQuotaStatutoryPost;
    }

    public function setMailboxQuotaStatutoryPost(int $mailboxQuotaStatutoryCollect): void
    {
        $this->mailboxQuotaStatutoryPost = $mailboxQuotaStatutoryCollect;
    }

    public function getMailboxQuotaNonStatutoryScanned(): int
    {
        return $this->mailboxQuotaNonStatutoryScanned;
    }

    public function setMailboxQuotaNonStatutoryScanned(int $mailboxQuotaNonStatutoryScanned): void
    {
        $this->mailboxQuotaNonStatutoryScanned = $mailboxQuotaNonStatutoryScanned;
    }

    public function getMailboxQuotaNonStatutoryCollect(): int
    {
        return $this->mailboxQuotaNonStatutoryCollect;
    }

    public function setMailboxQuotaNonStatutoryCollect(int $mailboxQuotaNonStatutoryCollect): void
    {
        $this->mailboxQuotaNonStatutoryCollect = $mailboxQuotaNonStatutoryCollect;
    }

    public function getMailboxQuotaNonStatutoryPost(): int
    {
        return $this->mailboxQuotaNonStatutoryPost;
    }

    public function setMailboxQuotaNonStatutoryPost(int $mailboxQuotaNonStatutoryPost): void
    {
        $this->mailboxQuotaNonStatutoryPost = $mailboxQuotaNonStatutoryPost;
    }

    public function getMailboxQuotaParcelCollect(): int
    {
        return $this->mailboxQuotaParcelCollect;
    }

    public function setMailboxQuotaParcelCollect(int $mailboxQuotaParcelCollect): void
    {
        $this->mailboxQuotaParcelCollect = $mailboxQuotaParcelCollect;
    }

    public function getMailboxQuotaParcelPost(): int
    {
        return $this->mailboxQuotaParcelPost;
    }

    public function setMailboxQuotaParcelPost(int $mailboxQuotaParcelPost): void
    {
        $this->mailboxQuotaParcelPost = $mailboxQuotaParcelPost;
    }

    public function getMailboxExtraQuotaPostItemFee(): float
    {
        return $this->mailboxExtraQuotaPostItemFee;
    }

    public function setMailboxExtraQuotaPostItemFee(float $mailboxExtraQuotaPostItemFee): void
    {
        $this->mailboxExtraQuotaPostItemFee = $mailboxExtraQuotaPostItemFee;
    }

    public function getMailboxExtraQuotaParcelFee(): float
    {
        return $this->mailboxExtraQuotaParcelFee;
    }

    public function setMailboxExtraQuotaParcelFee(float $mailboxExtraQuotaParcelFee): void
    {
        $this->mailboxExtraQuotaParcelFee = $mailboxExtraQuotaParcelFee;
    }

    public function getMailboxHandlingFeePostItem(): float
    {
        return $this->mailboxHandlingFeePostItem;
    }

    public function setMailboxHandlingFeePostItem(float $mailboxHandlingFeePostItem): void
    {
        $this->mailboxHandlingFeePostItem = $mailboxHandlingFeePostItem;
    }

    public function getMailboxHandlingFeeParcel(): float
    {
        return $this->mailboxHandlingFeeParcel;
    }

    public function setMailboxHandlingFeeParcel(float $mailboxHandlingFeeParcel): void
    {
        $this->mailboxHandlingFeeParcel = $mailboxHandlingFeeParcel;
    }

    public function getMailboxForwardingFeePostItem(): float
    {
        return $this->mailboxForwardingFeePostItem;
    }

    public function setMailboxForwardingFeePostItem(float $mailboxForwardingFeePostItem): void
    {
        $this->mailboxForwardingFeePostItem = $mailboxForwardingFeePostItem;
    }

    public function getMailboxForwardingFeeParcel(): float
    {
        return $this->mailboxForwardingFeeParcel;
    }

    public function setMailboxForwardingFeeParcel(float $mailboxForwardingFeeParcel): void
    {
        $this->mailboxForwardingFeeParcel = $mailboxForwardingFeeParcel;
    }

    public function getMailboxPayToReleaseFeePostItem(): float
    {
        return $this->mailboxPayToReleaseFeePostItem;
    }

    public function setMailboxPayToReleaseFeePostItem(float $mailboxPayToReleaseFeePostItem): void
    {
        $this->mailboxPayToReleaseFeePostItem = $mailboxPayToReleaseFeePostItem;
    }

    public function getMailboxPayToReleaseFeeParcel(): float
    {
        return $this->mailboxPayToReleaseFeeParcel;
    }

    public function setMailboxPayToReleaseFeeParcel(float $mailboxPayToReleaseFeeParcel): void
    {
        $this->mailboxPayToReleaseFeeParcel = $mailboxPayToReleaseFeeParcel;
    }

    public function getMailboxSettingPostItem(): int
    {
        return $this->mailboxSettingPostItem;
    }

    public function setMailboxSettingPostItem(int $mailboxSettingPostItem): void
    {
        $this->mailboxSettingPostItem = $mailboxSettingPostItem;
    }

    public function getMailboxSettingParcel(): int
    {
        return $this->mailboxSettingParcel;
    }

    public function setMailboxSettingParcel(int $mailboxSettingParcel): void
    {
        $this->mailboxSettingParcel = $mailboxSettingParcel;
    }

    public function getAdditionalInformation(): array
    {
        return $this->additionalInformation;
    }

    public function setAdditionalInformation(array $additionalInformation): void
    {
        $this->additionalInformation = $additionalInformation;
    }

    public function getAdditionalInformationProperty(string $propertyName): ?string
    {
        return ArrayHelper::get($this->getAdditionalInformation(), $propertyName, null);
    }

    public function isRequiredCompanyNumber(): ?bool
    {
        return $this->requiredCompanyNumber;
    }

    public function isRequiredIncorporationCompanyNumber(): bool
    {
        return $this->requiredIncorporatedCompanyNumber;
    }

    public function getAdditional(): string
    {
        return $this->additional ?? '';
    }

    public function getMarkUp(): float
    {
        return (float) $this->markUp;
    }

    public function isRequiredCorePackage(): bool
    {
        return $this->requiredCorePackage ?? false;
    }

    public function hasRenewalDtStartBehavior(): bool
    {
        return (bool) $this->renewalDtStartBehavior;
    }

    public function getRenewalDtStartBehavior(): ?string
    {
        return $this->renewalDtStartBehavior;
    }

    public function setRenewalDtStartBehavior(string $renewalDtStartBehavior): void
    {
        $this->renewalDtStartBehavior = $renewalDtStartBehavior;
    }

    public function getReference()
    {
        return $this->reference;
    }

    public function setReference($reference): void
    {
        $this->reference = $reference;
    }

    public function isShowInMyServicesPage(): bool
    {
        return $this->showInMyServicesPage;
    }

    public function setShowInMyServicesPage(bool $showInMyServicesPage = false): void
    {
        $this->showInMyServicesPage = $showInMyServicesPage;
    }

    public function isWholesaleOnly(): bool
    {
        return $this->customerType === self::CUSTOMER_TYPE_WHOLESALE_ONLY;
    }

    public function isRetailOnly(): bool
    {
        return $this->customerType === self::CUSTOMER_TYPE_RETAIL_ONLY;
    }

    public function isBankingPartnerPackage(): bool
    {
        return $this->isBankingPartnerPackage;
    }

    public function isBusinessPartnerPackage(): bool
    {
        return $this->isBusinessPartnerPackage;
    }

    public function isPartnerPackage(): bool
    {
        return $this->isBankingPartnerPackage || $this->isBusinessPartnerPackage;
    }

    public function requiresUkDirector(): bool
    {
        return $this->requiresUkDirector;
    }

    public function requiresAdultDirector(): bool
    {
        return $this->requiresAdultDirector;
    }

    protected function addCustomData()
    {
        parent::addCustomData();
        $this->price = (float) $this->getProperty('price');
        $this->productValue = (float) $this->getProperty('productValue');
        $this->associatedPrice = (float) $this->getProperty('associatedPrice');
        $this->wholesalePrice = (float) $this->getProperty('wholesalePrice');
        $this->offerPrice = (float) $this->getProperty('offerPrice');
        $this->requiredCompanyNumber = $this->getProperty('requiredCompanyNumber');
        $this->requiredIncorporatedCompanyNumber = $this->getProperty('requiredIncorporatedCompanyNumber');
        $this->onlyOurCompanies = $this->getProperty('onlyOurCompanies');
        $this->saveToCompany = $this->getProperty('saveToCompany');
        $this->responsibleEmails = $this->getProperty('responsibleEmails');
        $this->nonVatableValue = $this->getProperty('nonVatableValue');
        $this->nonVatableDescription = $this->getProperty('nonVatableDescription');
        $this->emailText = $this->getProperty('emailText');
        $this->basketText = $this->getProperty('basketText');
        $this->additional = $this->getProperty('additional');
        $this->lockCompany = (bool) $this->getProperty('lockCompany');
        $this->saving = $this->productValue - ($this->getPrice() ?: 0);
        $this->upgradeDescription = $this->getProperty('upgradeDescription');
        $this->upgradeImageId = $this->getProperty('upgradeImageId');
        $this->markUp = $this->getProperty('markUp');
        $this->associatedImageId = $this->getProperty('associatedImageId');
        $this->associatedText = $this->getProperty('associatedText');
        $this->associatedDescription = $this->getProperty('associatedDescription');
        $this->associatedIconClass = (string) $this->getProperty('associatedIconClass');
        $this->isFeefoEnabled = $this->getProperty('isFeefoEnabled');
        $this->isAutoRenewalAllowed = $this->getProperty('isAutoRenewalAllowed');
        $this->availableForPayByPhone = $this->getProperty(BasketProduct::PROPERTY_AVAILABLE_FOR_PAY_BY_PHONE);

        if ($this->getProperty('toolkitOfferTypes') !== null) {
            $this->toolkitOfferTypes = array_filter(explode(',', $this->getProperty('toolkitOfferTypes')));
        }

        $arr = ['onlyOneItem', 'maxQuantityOne', 'notApplyVat'];
        foreach ($arr as $key => $property) {
            $value = $this->getProperty($property);
            if ($value != null) {
                $this->$property = (bool) $value;
            }
        }

        // blacklisted products
        $arr = ['blacklistedEquivalentProducts', 'blacklistedContainedProducts'];
        foreach ($arr as $val) {
            $products = $this->getProperty($val);
            if ($products !== null) {
                $this->$val = explode(',', $products);
            }
        }

        // associated products
        if ($upsellProducts = $this->getProperty('associatedProducts3')) {
            $this->associatedProducts3 = explode(',', $upsellProducts);
        }

        $this->offerProductId = $this->getProperty('offerProductId');

        // conditioned by
        $this->conditionedById = $this->getProperty('conditionedBy');
        if ($this->conditionedById) {
            $this->conditionedBy = new Product($this->conditionedById);
        }

        // email attachments
        $emailCmsFileAttachments = $this->getProperty('emailCmsFileAttachments');
        if (!empty($emailCmsFileAttachments)) {
            $this->emailCmsFileAttachments = explode(',', $emailCmsFileAttachments);
        }

        $this->serviceTypeId = $this->getProperty('serviceTypeId');
        $this->setDuration($this->getProperty('duration'));
        $this->renewalProductId = $this->getProperty('renewalProductId');
        $this->optionalRenewalProductId = $this->getProperty('optionalRenewalProductId');
        $this->renewalDtStartBehavior = $this->getProperty('renewalDtStartBehavior');

        // vo eligible products
        $this->isVoServiceEligible = (bool) $this->getProperty('isVoServiceEligible');
        $this->voServiceDurationInMonths = (int) $this->getProperty('voServiceDurationInMonths');
        $this->isIdCheckRequired = $this->getProperty('isIdCheckRequired');

        $this->bankingEnabled = (bool) $this->getProperty('bankingEnabled');

        if ($this->getProperty('bankingOptions') !== null) {
            $this->bankingOptions = array_filter(explode(',', $this->getProperty('bankingOptions')));
        }

        $this->bankingRequired = (bool) $this->getProperty('bankingRequired');

        $this->printedCertificateOptionEnabled = (bool) $this->getProperty('printedCertificateOptionEnabled');

        $this->removableFromBasket = $this->getProperty('removableFromBasket') !== null ? $this->getProperty('removableFromBasket') : $this->removableFromBasket;
        $this->removeFromBasketConfirmation = (string) $this->getProperty('removeFromBasketConfirmation');

        $this->isInitialProduct = (bool) $this->getProperty('isInitialProduct');
        $this->isRenewalProduct = (bool) $this->getProperty('isRenewalProduct');
        $this->showInMyServicesPage = (bool) $this->getProperty('showInMyServicesPage');

        $this->sageNominalCode = $this->getProperty('sageNominalCode');
        $this->registrationReviewEnabled = (bool) $this->getProperty('registrationReviewEnabled');

        if ($this->isRenewalProduct()) {
            $this->renewalEmailPrice = (float) $this->getProperty('renewalEmailPrice');
        }

        $this->specialPrice = (float) $this->getProperty('specialPrice');

        $this->requiredCorePackage = $this->getProperty('requiredCorePackage');
        $this->customAssociatedIcon = $this->getProperty('customAssociatedIcon');

        $this->quotas = $this->getProperty('quotas');

        $this->mailboxAcceptanceStatutory = intval($this->getProperty('mailbox-acceptance-statutory'));
        $this->mailboxAcceptanceNonStatutory = intval($this->getProperty('mailbox-acceptance-non-statutory'));
        $this->mailboxAcceptanceParcel = intval($this->getProperty('mailbox-acceptance-parcel'));
        $this->mailboxQuotaStatutoryScanned = intval($this->getProperty('mailbox-quota-statutory-scanned'));
        $this->mailboxQuotaStatutoryCollect = intval($this->getProperty('mailbox-quota-statutory-collect'));
        $this->mailboxQuotaStatutoryPost = intval($this->getProperty('mailbox-quota-statutory-post'));
        $this->mailboxQuotaNonStatutoryScanned = intval($this->getProperty('mailbox-quota-non-statutory-scanned'));
        $this->mailboxQuotaNonStatutoryCollect = intval($this->getProperty('mailbox-quota-non-statutory-collect'));
        $this->mailboxQuotaNonStatutoryPost = intval($this->getProperty('mailbox-quota-non-statutory-post'));
        $this->mailboxQuotaParcelCollect = intval($this->getProperty('mailbox-quota-parcel-collect'));
        $this->mailboxQuotaParcelPost = intval($this->getProperty('mailbox-quota-parcel-post'));
        $this->mailboxExtraQuotaPostItemFee = floatval($this->getProperty('mailbox-extra-quota-post-item-fee'));
        $this->mailboxExtraQuotaParcelFee = floatval($this->getProperty('mailbox-extra-quota-parcel-fee'));
        $this->mailboxHandlingFeePostItem = floatval($this->getProperty('mailbox-handling-fee-post-item'));
        $this->mailboxHandlingFeeParcel = floatval($this->getProperty('mailbox-handling-fee-parcel'));
        $this->mailboxForwardingFeePostItem = floatval($this->getProperty('mailbox-forwarding-fee-post-item'));
        $this->mailboxForwardingFeeParcel = floatval($this->getProperty('mailbox-forwarding-fee-parcel'));
        $this->mailboxSettingPostItem = intval($this->getProperty('mailbox-setting-post-item'));
        $this->mailboxSettingParcel = intval($this->getProperty('mailbox-setting-parcel'));
        $this->mailboxPayToReleaseFeePostItem = floatval($this->getProperty('mailbox-pay-to-release-fee-post-item'));
        $this->mailboxPayToReleaseFeeParcel = floatval($this->getProperty('mailbox-pay-to-release-fee-parcel'));

        $this->customerType = $this->getProperty('customerType') ?? self::CUSTOMER_TYPE_ALL;

        $this->requiresUkDirector = boolval($this->getProperty('requiresUkDirector'));
        $this->requiresAdultDirector = boolval($this->getProperty('requiresAdultDirector'));
        $this->isBankingPartnerPackage = boolval($this->getProperty('isBankingPartnerPackage'));
        $this->isBusinessPartnerPackage = boolval($this->getProperty('isBusinessPartnerPackage'));
    }

    private function matchesCompany(BasketProduct $item): bool
    {
        if (empty($item->getCompanyId()) || empty($this->getCompanyId())) {
            return true;
        }

        return $item->getCompanyId() === $this->getCompanyId();
    }
}
