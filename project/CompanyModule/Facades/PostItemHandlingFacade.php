<?php

declare(strict_types=1);

namespace CompanyModule\Facades;

use CompanyModule\Entities\Settings\PostItemHandlingSetting;
use CompanyModule\Repositories\CompanySettingsRepository;
use Doctrine\ORM\NonUniqueResultException;
use Entities\Company;
use Exceptions\Technical\NodeException;
use MailScanModule\Deciders\MailboxTierDecider;
use MailScanModule\Dto\MailroomPostItemData;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Exceptions\NotAMailboxProductException;
use MailScanModule\Helpers\MailboxProductPropertyHelper;
use MailScanModule\Providers\MailboxProductProvider;
use Models\Products\BasketProduct;

class PostItemHandlingFacade
{
    public const POST_ITEM_TYPES = [
        PostItemTypeEnum::TYPE_STATUTORY,
        PostItemTypeEnum::TYPE_NON_STATUTORY,
    ];
    public const KEY_POST_ITEM_HANDLING = PostItemHandlingSetting::KEY_POST_ITEM_HANDLING;
    public const KEY_PARCEL_HANDLING = PostItemHandlingSetting::KEY_PARCEL_HANDLING;
    public const VALUE_ITEM_SCAN_ONLY = PostItemHandlingSetting::VALUE_ITEM_SCAN_ONLY;
    public const VALUE_ITEM_SCAN_AND_COLLECT = PostItemHandlingSetting::VALUE_ITEM_SCAN_AND_COLLECT;
    public const VALUE_ITEM_SCAN_AND_POST = PostItemHandlingSetting::VALUE_ITEM_SCAN_AND_POST;
    public const VALUE_PARCEL_COLLECT = PostItemHandlingSetting::VALUE_PARCEL_COLLECT;
    public const VALUE_PARCEL_POST = PostItemHandlingSetting::VALUE_PARCEL_POST;

    public function __construct(
        private readonly CompanySettingsRepository $companySettingsRepository,
        private readonly MailboxProductProvider $mailboxProductProvider,
    ) {
    }

    /**
     * @throws NonUniqueResultException
     */
    public function getHandlingSettingByType(Company $company, MailroomPostItemData $postItem, BasketProduct $product, int $tier): int
    {
        if ($tier <= MailboxTierDecider::TIER_1) {
            return MailboxProductPropertyHelper::getHandlingSettingByFormat(
                $product,
                MailboxProductPropertyHelper::FORMATS[$postItem->getFormat()]
            );
        }

        return $this->getPostItemHandlingSetting($company)->getHandlingSettingByType(
            $postItem->isParcel() ? self::KEY_PARCEL_HANDLING : self::KEY_POST_ITEM_HANDLING
        );
    }


    /**
     * @throws NodeException
     * @throws NotAMailboxProductException
     * @throws NonUniqueResultException
     */
    public function setHandlingSettingByType(Company $company, string $type, int $value): void
    {
        $setting = $this->getPostItemHandlingSetting($company);
        $setting->setHandlingSettingByType($this->getKeyFromType($type), $value);

        $this->persistSetting($setting);
    }

    /**
     * @throws NonUniqueResultException
     * @throws NotAMailboxProductException
     * @throws NodeException
     */
    public function getPostItemHandlingSetting(Company $company): ?PostItemHandlingSetting
    {
        /** @var PostItemHandlingSetting|null $settings */
        $settings = $this->companySettingsRepository->getSettingByClass($company, PostItemHandlingSetting::class);

        if ($settings) {
            return $settings;
        }

        $mailboxProduct = $this->mailboxProductProvider->getInitialProductFromCompany($company);

        if (is_null($mailboxProduct)) {
            return null;
        }

        return new PostItemHandlingSetting(
            $company,
            MailboxProductPropertyHelper::getHandlingSettingByFormat($mailboxProduct, MailboxProductPropertyHelper::FORMAT_POST_ITEM),
            MailboxProductPropertyHelper::getHandlingSettingByFormat($mailboxProduct, MailboxProductPropertyHelper::FORMAT_PARCEL)
        );
    }

    /**
     * @throws NotAMailboxProductException
     * @throws NodeException
     * @throws NonUniqueResultException
     */
    public function resetSetting(Company $company): void
    {
        $setting = $this->getPostItemHandlingSetting($company);

        if (!$setting) {
            return;
        }

        $product = $this->mailboxProductProvider->getInitialProductFromCompany($company);
        $setting->setHandlingSettingByType(
            self::KEY_POST_ITEM_HANDLING,
            MailboxProductPropertyHelper::getHandlingSettingByFormat($product, MailboxProductPropertyHelper::FORMAT_POST_ITEM),
        );
        $setting->setHandlingSettingByType(
            self::KEY_PARCEL_HANDLING,
            MailboxProductPropertyHelper::getHandlingSettingByFormat($product, MailboxProductPropertyHelper::FORMAT_PARCEL),
        );

        $this->persistSetting($setting);
    }

    private function persistSetting(PostItemHandlingSetting $setting): void
    {
        $this->companySettingsRepository->persist($setting);
        $this->companySettingsRepository->flush();
    }

    private function getKeyFromType(string $type): string
    {
        return in_array(PostItemTypeEnum::from($type), self::POST_ITEM_TYPES)
            ? self::KEY_POST_ITEM_HANDLING
            : self::KEY_PARCEL_HANDLING;
    }

    public function clearDoctrineMemory(): void
    {
        $this->companySettingsRepository->getEntityManager()->clear();
    }
}
