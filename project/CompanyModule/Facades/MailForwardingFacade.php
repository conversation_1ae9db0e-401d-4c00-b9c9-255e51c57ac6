<?php

declare(strict_types=1);

namespace CompanyModule\Facades;

use CompanyModule\Entities\Settings\MailForwardingAddressSetting;
use CompanyModule\Entities\Settings\MailForwardingQuotaSetting;
use CompanyModule\Entities\Settings\PostItemHandlingSetting;
use CompanyModule\Repositories\CompanySettingsRepository;
use Doctrine\ORM\NonUniqueResultException;
use Entities\Company;

class MailForwardingFacade
{
    public function __construct(
        private readonly CompanySettingsRepository $companySettingsRepository,
    ) {
    }

    public function hasQuotas(Company $company): bool
    {
        try {
            return (bool) $this->companySettingsRepository->getSettingByClass($company, MailForwardingQuotaSetting::class);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * @throws NonUniqueResultException
     */
    public function getQuotas(Company $company, string $type, string $handlingOption): int
    {
        return $this->getMailForwardingQuota($company)->getQuotasByTypeAndHandlingOption($type, $handlingOption);
    }

    /**
     * @throws NonUniqueResultException
     */
    public function getQuotasByType(Company $company, string $type, string $handlingOption): int
    {
        return $this->getMailForwardingQuota($company)->getQuotasByTypeAndHandlingOption($type, $handlingOption);
    }

    /**
     * @throws NonUniqueResultException
     */
    public function addQuotas(Company $company, int $amount, string $type, string $handlingOption, bool $persist = true): int
    {
        $setting = $this->getMailForwardingQuota($company);
        $finalAmount = $setting->addQuotasByType($amount, $type, $handlingOption);

        if ($persist) {
            $this->persistSetting($setting);
        }

        return $finalAmount;
    }

    /**
     * @throws NonUniqueResultException
     */
    public function updateQuotas(Company $company, int $amount, string $type, string $handlingOption): int
    {
        $setting = $this->getMailForwardingQuota($company);
        $finalAmount = $setting->updateQuotasByTypeAndHandlingOption($amount, $type, $handlingOption);

        $this->persistSetting($setting);

        return $finalAmount;
    }

    /**
     * @throws NonUniqueResultException
     */
    public function subtractQuotas(Company $company, int $quotas, string $type, string $handlingOption): int
    {
        $setting = $this->getMailForwardingQuota($company);
        $finalAmount = $setting->subtractQuotasByType($quotas, $type, $handlingOption);
        $this->persistSetting($setting);

        return $finalAmount;
    }

    /**
     * @deprecated use MailScanModule\Facades\MailForwardingAddressFacade::getMailForwardingAddress
     * @throws NonUniqueResultException
     */
    public function getMailForwardingAddress(Company $company, bool $allowNullSetting = true): MailForwardingAddressSetting
    {
        /* @var MailForwardingAddressSetting|null $setting */
        $setting = $this->companySettingsRepository->getSettingByClass($company, MailForwardingAddressSetting::class);

        if (empty($setting)) {
            return $allowNullSetting
                ? new MailForwardingAddressSetting($company)
                : new MailForwardingAddressSetting(
                    $company,
                    $company->getPremise(),
                    $company->getStreet(),
                    $company->getThoroughfare(),
                    $company->getPostTown(),
                    $company->getCountry(),
                    $company->getPostcode()
                );
        }

        return $setting; /** @phpstan-ignore-line */
    }

    public function setMailForwardingAddress(MailForwardingAddressSetting $settings): void
    {
        $this->companySettingsRepository->persist($settings);
        $this->companySettingsRepository->flush();
    }

    public function setPostItemHandlingSetting(PostItemHandlingSetting $settings): void
    {
        $this->companySettingsRepository->persist($settings);
        $this->companySettingsRepository->flush();
    }

    /**
     * @throws NonUniqueResultException
     */
    private function getMailForwardingQuota(Company $company): MailForwardingQuotaSetting
    {
        /** @var MailForwardingQuotaSetting|null $setting */
        $setting = $this->companySettingsRepository->getSettingByClass($company, MailForwardingQuotaSetting::class);

        return $setting ?? new MailForwardingQuotaSetting($company);
    }

    private function persistSetting(MailForwardingQuotaSetting $setting): void
    {
        $this->companySettingsRepository->persist($setting);
        $this->companySettingsRepository->flush();
    }

    public function clearDoctrineMemory(): void
    {
        $this->companySettingsRepository->getEntityManager()->clear();
    }
}
